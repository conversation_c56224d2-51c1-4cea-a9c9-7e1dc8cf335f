import type {
  Conversation,
  Coupon,
  GetStarted,
  IrClickId,
  Notification,
  OnboardingData,
  OnboardingSteps,
} from '@/data/types';
import { atom } from 'jotai';
import { atomWithStorage } from 'jotai/utils';

export const interceptorInjected = atom(false);

export const getStartedAtom = atomWithStorage<GetStarted>('get-started', {});
export const onboardingDiscountAtom = atomWithStorage<{
  type: 'referral' | 'coupon';
  code: string;
} | null>('discount', null);

export const onboardingDataAtom = atom<OnboardingData | null>(null);
export const promoCouponDataAtom = atom<Coupon | null | undefined>(null);
export const onboardingVersionAtom = atom<'v1' | 'legacy' | null>(null);

export const notificationAtom = atom<Notification | null>(null);

export const onboardingStepsAtom = atom<OnboardingSteps>({
  stepName: '',
  percentage: 0,
  back: '',
});

export const chatDataAtom = atom<Conversation | null>(null);

export const irClickIdAtom = atomWithStorage<IrClickId | null>(
  'irclickid',
  null,
);

export const lastRequestedUrlAtom = atom<string | null>(null);

export const messagesCountAtom = atom<number>(0);
