import type { ShippingInfoDto } from '@modules/onboarding/dto/shipping-info.dto';
import type { DesiredTreatment } from '@modules/onboarding/use-cases/onboarding-desired-treatment.use-case';

// Pre-signup data types
export interface PreSignupData {
  state?: string;
  email?: string;
  firstName?: string;
  lastName?: string;
  phone?: string;
  password?: string;
  getPromotionsSMS?: boolean;
}

// Questionnaire step data types
export interface QuestionnaireStepData {
  birthDate?: string;
  gender?: 'male' | 'female';
  height?: number;
  weight?: number;
  desiredWeight?: number;
  usingGLP1?: 'yes' | 'no';
  isPregnant?: 'yes' | 'no';
  haveDiabetes?: 'yes' | 'no';
  doctorVisits?: 'yes' | 'no';
  hasAllergies?: 'yes' | 'no';
  allergies?: string[];
  medications?: string[];
  objectives?: string[];
  medicalConditions?: string[];
  qualifyingConditions?: string[];
  additionalInformation?: string;
  priorConditions?: string[];
}

// Post-questionnaire data types
export interface PostQuestionnaireData {
  productType?: string;
  products?: DesiredTreatment[];
  'id-photo'?: string;
  'face-photo'?: string;
  shippingInfo?: ShippingInfoDto;
  paymentIntent?: string;
}

// Complete context type
export interface OnboardingV1Context {
  // Pre-signup data
  state?: string;
  email?: string;
  firstName?: string;
  lastName?: string;
  phone?: string;
  password?: string;
  getPromotionsSMS?: boolean;

  // State management
  rejected?: boolean;
  questionnaireCompleted: boolean;
  rejectedReason?: string;

  // Auth data (populated after user creation)
  authData?: any;

  // Questionnaire data (structured)
  questionnaire: {
    birthDate?: string;
    gender?: 'male' | 'female';
    height?: number;
    weight?: number;
    desiredWeight?: number;
    usingGLP1?: 'yes' | 'no';
    isPregnant?: 'yes' | 'no';
    haveDiabetes?: 'yes' | 'no';
    doctorVisits?: 'yes' | 'no';
    hasAllergies?: 'yes' | 'no';
    allergies?: string[];
    medications?: string[];
    objectives?: string[];
    medicalConditions?: string[];
    qualifyingConditions?: string[];
    additionalInformation?: string;
  };

  // Post-questionnaire data
  pharmacyId?: string;
  productType?: string;
  products?: DesiredTreatment[];
  'id-photo'?: string;
  'face-photo'?: string;
  shippingInfo?: ShippingInfoDto;
  paymentIntent?: string;
}

// Event types with proper typing
export type OnboardingV1Events =
  // Navigation events
  | {
      type: 'next';
      value?: PreSignupData | QuestionnaireStepData | PostQuestionnaireData;
    }
  | { type: 'back' }

  // Submit events with specific payloads
  | {
      type: 'submit';
      subtype: 'createAccount';
      value: {
        email: string;
        password: string;
        phone: string;
        firstName: string;
        lastName: string;
        getPromotionsSMS?: boolean;
      };
    }
  | {
      type: 'submit';
      subtype: 'waitingList';
      value: {
        email: string;
        state: string;
      };
    }
  | {
      type: 'submit';
      subtype: 'validateName';
      value: {
        firstName: string;
        lastName: string;
      };
    }

  // Questionnaire specific answer events
  | { type: 'male' }
  | { type: 'female' }
  | { type: 'yes' }
  | { type: 'no' };

// Input type for machine initialization
export interface OnboardingV1Input {
  version: 'v1';
  existingData?: Partial<OnboardingV1Context>;
  statePersistence?: any; // Service to check state availability
}

// Actor input/output types for type-safe invocations
export interface CheckStateEnabledInput {
  stateCode?: string;
}

export interface CheckStateEnabledOutput {
  stateCode: string;
}

export interface CreateUserInput {
  context: OnboardingV1Context;
}

export interface CreateUserOutput {
  success: boolean;
  authData?: any;
}

export interface AddToWaitingListInput {
  email: string;
  state: string;
}

export interface AddToWaitingListOutput {
  success: boolean;
}

export interface ValidateNameInput {
  firstName: string;
  lastName: string;
}

export interface ValidateNameOutput {
  valid: boolean;
  errors?: string[];
}
