import { CacheService } from '@/modules/cache/cache.service';
import { RedlockService } from '@/modules/cache/redlock.service';
import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import ShortUniqueId from 'short-unique-id';

export const PRIMARY_INSTANCE_KEY = 'primary_instance';

@Injectable()
export class OrchestrationService {
  private readonly logger = new Logger(OrchestrationService.name);

  public static instanceId: string;
  public static instanceVersion: string;
  public static instanceLabel: string | null = null;
  public static primaryInstanceId: string | null = null;

  constructor(
    private readonly configService: ConfigService,
    private readonly cache: CacheService,
    private readonly redlock: RedlockService,
  ) {
    OrchestrationService.instanceVersion =
      this.configService.get<string>('API_VERSION') || '0.0';
    OrchestrationService.instanceId =
      configService.get('API_INSTANCE_ID') ?? this.generateInstanceId();
    OrchestrationService.__generateLabel();

    this.logger.debug(
      `Instance initialized with ID: ${OrchestrationService.instanceId}`,
    );
  }

  static __setPrimaryInstanceId(instanceId: string | null) {
    OrchestrationService.primaryInstanceId = instanceId;
    OrchestrationService.__generateLabel();
  }

  static __generateLabel() {
    const version =
      OrchestrationService.instanceVersion &&
      OrchestrationService.instanceVersion !== '0.0'
        ? `v${OrchestrationService.instanceVersion.slice(0, 7)}`
        : '0.0';
    const primary =
      OrchestrationService.primaryInstanceId === OrchestrationService.instanceId
        ? 'primary'
        : 'secondary';

    OrchestrationService.instanceLabel = `${OrchestrationService.instanceId}-${version}-${primary}`;

    return OrchestrationService.instanceLabel;
  }

  getInstanceId(): string {
    return OrchestrationService.instanceId;
  }

  private generateInstanceId(): string {
    const uid = new ShortUniqueId({ length: 8 });
    return uid.rnd();
  }

  async getPrimaryInstanceId() {
    const primaryInstance = await this.cache.get<string>(PRIMARY_INSTANCE_KEY);
    return primaryInstance || null;
  }

  async isPrimaryInstance() {
    const primaryInstance = await this.cache.get<string>(PRIMARY_INSTANCE_KEY);
    return primaryInstance === this.getInstanceId();
  }

  async runWithLock<T>(
    options: {
      lockKey: string;
      ttl: number;
      thisInstanceMustBePrimary?: boolean;
    },
    fn: () => Promise<T>,
  ) {
    if (options.thisInstanceMustBePrimary) {
      const isPrimary = await this.isPrimaryInstance();
      if (!isPrimary) {
        this.logger.debug(
          {
            context: {
              name: OrchestrationService.name,
              instance: {
                version: OrchestrationService.instanceVersion,
                label: OrchestrationService.instanceLabel,
                id: OrchestrationService.instanceId,
                isPrimary: false,
              },
            },
          },
          `Cannot run function with lock: ${options.lockKey} because this instance is not the primary instance.`,
          { options },
        );
        return;
      }
    }

    return this.redlock.lockWhileRunning(
      `lock:orchestration-run:${options.lockKey}`,
      options.ttl,
      fn,
    );
  }
}
