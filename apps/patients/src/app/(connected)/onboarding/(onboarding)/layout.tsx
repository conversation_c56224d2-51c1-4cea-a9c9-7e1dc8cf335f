'use client';

import type React from 'react';
import { useEffect } from 'react';
import OnboardingLayout from '@/components/onboarding/OnboardingLayout';
import { onboardingDataAtom, onboardingStepsAtom } from '@/store/store';
import { useSetAtom } from 'jotai';
import { useAtomValue } from 'jotai/index';

const OnboardLayout = ({ children }: { children: React.ReactNode }) => {
  const onboarding = useAtomValue(onboardingDataAtom);
  const setSteps = useSetAtom(onboardingStepsAtom);

  useEffect(() => {
    if (!onboarding) return;

    const { stepName, percentage } = onboarding;

    setSteps({
      stepName,
      percentage,
    });
  }, [onboarding, setSteps]);

  return <OnboardingLayout withStepCounter={true}>{children}</OnboardingLayout>;
};
export default OnboardLayout;
