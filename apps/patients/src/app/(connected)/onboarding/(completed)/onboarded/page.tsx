'use client';

import type { DashboardData } from '@/data/types';
import { useEffect, useRef, useState } from 'react';
import Image from 'next/image';
import Link from 'next/link';
import arrow from '@/assets/svg/arrow-circle.svg';
import OnboardingLayout from '@/components/onboarding/OnboardingLayout';
import OnboardingTitle from '@/components/onboarding/OnboardingTitle';
import { Button } from '@/components/ui/button';
import {
  interceptorInjected,
  onboardingDataAtom,
  onboardingStepsAtom,
} from '@/store/store';
import { useQuery, useQueryClient } from '@tanstack/react-query';
import { useSetAtom } from 'jotai';
import { useAtom, useAtomValue } from 'jotai/index';

import { Loader } from '@willow/ui/loader';
import { apiClient } from '@willow/utils/api/client';

const useIsOnboarded = (injected: boolean) => {
  const [shouldPoll, setShouldPoll] = useState(true);
  const [retryTrigger, setRetryTrigger] = useState(0);
  const abortControllerRef = useRef<AbortController | null>(null);
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Clean up on unmount
  useEffect(() => {
    return () => {
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, []);

  const { data } = useQuery({
    queryKey: ['onboarded', retryTrigger],
    queryFn: async () => {
      // Clean up any previous controller/timeout
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }

      // Create new abort controller for this request
      abortControllerRef.current = new AbortController();

      // Set a timeout to force retry if request takes too long
      timeoutRef.current = setTimeout(() => {
        console.log('Request timeout, forcing retry');

        // Cancel the current request
        if (abortControllerRef.current) {
          abortControllerRef.current.abort();
          abortControllerRef.current = null;
        }

        // Force a new request by updating the retry trigger
        setRetryTrigger((prev) => prev + 1);
      }, 8000); // 8 second timeout

      try {
        const res = await apiClient.get(`/onboarding/onboarded`, {
          signal: abortControllerRef.current.signal,
        });

        // Clear the timeout since the request completed
        if (timeoutRef.current) {
          clearTimeout(timeoutRef.current);
          timeoutRef.current = null;
        }

        const data = res.data.dashboard as DashboardData;
        setShouldPoll(!data);
        return data;
      } catch (error) {
        // Clear the timeout
        if (timeoutRef.current) {
          clearTimeout(timeoutRef.current);
          timeoutRef.current = null;
        }

        // Don't throw if it was aborted - this will be followed by a retry
        if ((error as any)?.name === 'AbortError') {
          console.log('Request aborted, retrying...');
          return null; // Return null to avoid error state during retry
        }

        console.error('Error checking onboarded status:', error);
        throw error;
      }
    },
    enabled: injected,
    refetchInterval: shouldPoll ? 1000 : false,
    refetchIntervalInBackground: true,
    retry: 3, // Allow up to 3 automatic retries for non-timeout errors
  });

  return { data };
};

const Onboarded = () => {
  const [onboarding, setOnboarding] = useAtom(onboardingDataAtom);
  const injected = useAtomValue(interceptorInjected);
  const setSteps = useSetAtom(onboardingStepsAtom);

  const queryClient = useQueryClient();

  const { data } = useIsOnboarded(injected);

  useEffect(() => {
    if (!onboarding) return;
    const { stepName, percentage } = onboarding;
    setSteps({
      stepName: stepName || 'Welcome to Willow',
      percentage: percentage || 100,
    });
  }, [onboarding, setSteps]);

  useEffect(() => {
    if (!data) return;
    setOnboarding(null);
    queryClient.setQueryData(['profile'], data);
    void queryClient.refetchQueries({
      queryKey: ['status'],
    });
    void queryClient.invalidateQueries({ queryKey: ['profile'] });
  }, [data, setOnboarding, queryClient]);

  return (
    <OnboardingLayout withStepCounter={true}>
      <OnboardingTitle
        title="Success! Your visit has been sent to a Willow doctor."
        subtitle="Okay great! Willow Physician is reviewing your completed medical questionnaire, and will reply to you shortly."
      >
        <div className="flex w-full flex-col gap-10">
          {!data && (
            <div className="flex flex-col gap-[26px]">
              <Loader
                color={'white'}
                description="Setting up your patient dashboard..."
              />
            </div>
          )}

          {data && (
            <div className="flex flex-col gap-14">
              <div className="flex flex-col gap-9 md:gap-10">
                <div className="flex flex-col gap-2">
                  <span className="text-xl font-semibold leading-normal tracking-tight text-white">
                    What's Next?
                  </span>

                  <ul className="flex list-disc flex-col gap-4 pl-4 md:gap-1.5">
                    <li className="text-lg font-normal leading-normal tracking-tight text-white">
                      You will be paired with a board-certified Willow Physician
                      in your state
                    </li>
                    <li className="text-lg font-normal leading-normal tracking-tight text-white">
                      The Willow Physician will review your medical
                      questionnaire and determine the treatment plan that is the
                      best fit for you
                    </li>
                    <li className="text-lg font-normal leading-normal tracking-tight text-white">
                      They will reach out to you through the patient portal to
                      address any questions
                    </li>
                    <li className="text-lg font-normal leading-normal tracking-tight text-white">
                      If they determine the right treatment plan for you, they
                      will prescribe and process your treatment.
                    </li>
                  </ul>
                </div>

                <div className="flex flex-col gap-2">
                  <span className="text-xl font-semibold leading-normal tracking-tight text-white">
                    Have a question?
                  </span>

                  <span className="text-lg font-normal leading-normal tracking-tight text-white">
                    Please go to our{' '}
                    <a
                      href="https://docs.startwillow.com/en/"
                      target="_blank"
                      className="underline hover:opacity-85"
                    >
                      Help Center
                    </a>{' '}
                    where you get answers to commonly asked questions about
                    billing, shipment status, etc.
                  </span>
                </div>
              </div>

              <Link href="/dashboard">
                <Button
                  size="lg"
                  variant="electric"
                  className="flex w-full max-w-none justify-center gap-2"
                >
                  <span>Go to Patient Dashboard</span>
                  <Image
                    alt="arrow"
                    src={arrow}
                    style={{ objectFit: 'contain' }}
                  />
                </Button>
              </Link>
            </div>
          )}
        </div>
      </OnboardingTitle>
    </OnboardingLayout>
  );
};

export default Onboarded;
