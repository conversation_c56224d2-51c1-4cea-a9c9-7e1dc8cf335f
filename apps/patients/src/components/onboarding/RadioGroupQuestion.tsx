import type { OnboardingProps } from '@/app/(connected)/onboarding/(onboarding)/questionnaire/[slug]/page';
import { useState } from 'react';
import Image from 'next/image';
import arrow from '@/assets/svg/arrow-circle.svg';
import OnboardingTitle from '@/components/onboarding/OnboardingTitle';
import { Button } from '@/components/ui/button';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLoader,
  FormMessage,
} from '@/components/ui/form';
import { Label } from '@/components/ui/label';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { useOnboardingService } from '@/hooks/useOnboardingService';
import { getStartedAtom } from '@/store/store';
import { zodResolver } from '@hookform/resolvers/zod';
import { useSetAtom } from 'jotai';
import { useForm } from 'react-hook-form';
import { z } from 'zod';

interface RadioOption {
  value: string;
  label: string;
}

interface RadioGroupQuestionConfig {
  fieldName: string;
  title: string;
  subtitle?: string;
  options: RadioOption[];
  schemaValues: [string, string];
  errorMessage?: string;
  getContextValue: (context: any) => string;
  updateAtom?: (value: string) => any;
}

interface RadioGroupQuestionProps extends OnboardingProps {
  config: RadioGroupQuestionConfig;
}

const RadioGroupQuestion = ({
  callback,
  data,
  config,
}: RadioGroupQuestionProps) => {
  const {
    fieldName,
    title,
    subtitle,
    options,
    schemaValues,
    errorMessage = 'Please select an option',
    getContextValue,
    updateAtom,
  } = config;

  const setGetStarted = useSetAtom(getStartedAtom);
  const [isLoading, setIsLoading] = useState(false);

  // Track if we have pre-selected data
  const initialValue = getContextValue(data.context);
  const hasPreselectedData = !!initialValue;

  const schema = z.object({
    [fieldName]: z.enum(schemaValues, {
      required_error: errorMessage,
    }),
  });

  type FormType = z.infer<typeof schema>;

  const form = useForm<FormType>({
    resolver: zodResolver(schema),
    values: {
      [fieldName]: initialValue,
    } as FormType,
  });

  const { sendQuestionnaireEvent, isLoading: isTransitioning } =
    useOnboardingService();

  const onSubmit = async (formData: FormType) => {
    try {
      setIsLoading(true);
      const value = String(formData[fieldName]);
      const response = await sendQuestionnaireEvent(value);

      if (updateAtom) {
        setGetStarted((prev) => ({
          ...prev,
          ...updateAtom(value),
        }));
      }

      callback(response.data);
    } catch (e) {
      setIsLoading(false);
      const errorMessage =
        e &&
        typeof e === 'object' &&
        'response' in e &&
        e.response &&
        typeof e.response === 'object' &&
        'data' in e.response &&
        e.response.data &&
        typeof e.response.data === 'object' &&
        'message' in e.response.data
          ? (e.response.data as { message: string }).message
          : 'An error occurred';
      form.setError(fieldName as any, { message: errorMessage });
    }
  };

  // Handle radio change with auto-submit logic
  const handleRadioChange = (value: string) => {
    // Update the form field value first for visual feedback
    form.setValue(fieldName as any, value);

    // Only auto-submit if there was no pre-selected data initially
    if (!hasPreselectedData) {
      // Small delay to show the selection before submitting
      setTimeout(() => {
        void form.handleSubmit(onSubmit)();
      }, 150);
    }
  };

  return (
    <OnboardingTitle title={title} subtitle={subtitle}>
      <Form {...form}>
        <form
          onSubmit={form.handleSubmit(onSubmit)}
          className="flex w-full flex-col items-center gap-10"
        >
          <FormLoader isLoading={isLoading || isTransitioning}>
            <FormField
              control={form.control}
              name={fieldName as any}
              render={({ field }) => (
                <FormItem className="grid w-full max-w-2xl gap-1 py-2">
                  <FormControl>
                    <RadioGroup
                      className="flex w-full flex-col items-center gap-5 md:items-start"
                      onValueChange={handleRadioChange}
                      value={field.value}
                      defaultValue={field.value}
                    >
                      {options.map((option, index) => (
                        <div
                          key={option.value}
                          className="relative flex h-20 w-full rounded-lg bg-glass"
                        >
                          <div className="ml-9 flex h-full w-full items-center">
                            <div className="flex h-full w-full items-center space-x-2">
                              <RadioGroupItem
                                className="border-white"
                                value={option.value}
                                id={`r${index + 1}`}
                              />
                              <Label
                                className="flex h-full w-full cursor-pointer items-center text-white"
                                htmlFor={`r${index + 1}`}
                              >
                                {option.label}
                              </Label>
                            </div>
                          </div>
                        </div>
                      ))}
                    </RadioGroup>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            {hasPreselectedData && (
              <Button
                size="lg"
                variant="electric"
                className="flex w-full max-w-none justify-between"
                type="submit"
                disabled={isTransitioning}
              >
                <span>CONTINUE</span>
                <Image
                  alt="arrow"
                  src={arrow}
                  style={{ objectFit: 'contain' }}
                />
              </Button>
            )}
          </FormLoader>
        </form>
      </Form>
    </OnboardingTitle>
  );
};

export default RadioGroupQuestion;
