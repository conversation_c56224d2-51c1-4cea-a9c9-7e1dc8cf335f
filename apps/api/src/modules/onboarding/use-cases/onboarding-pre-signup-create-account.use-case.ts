import { ContextService } from '@/modules/context/context.service';
import { PatientSignUpUseCase } from '@/modules/patient/use-cases/patient-sign-up.use-case';
import { PrismaService } from '@/modules/prisma/prisma.service';
import { ForbiddenException, Injectable, Logger } from '@nestjs/common';
import { fromPromise } from 'xstate';

import type { OnboardingV1Events } from '../states/versions/onboarding-v1.types';
import { PreSignupCreateAccountDto } from '../dto/pre-signup/create-account.dto';
import { OnboardingCookieService } from '../services/onboarding-cookie.service';
import { OnboardingStateService } from '../services/onboarding-state.service';
import {
  OnboardingPreSignupBaseUseCase,
  PreSignupResponse,
} from './onboarding-pre-signup-base.use-case';

@Injectable()
export class OnboardingPreSignupCreateAccountUseCase extends OnboardingPreSignupBaseUseCase {
  protected readonly logger = new Logger(
    OnboardingPreSignupCreateAccountUseCase.name,
  );

  constructor(
    cookieService: OnboardingCookieService,
    onboardingStateService: OnboardingStateService,
    prisma: PrismaService,
    private readonly patientSignUpUseCase: PatientSignUpUseCase,
    private readonly contextService: ContextService,
  ) {
    super(cookieService, onboardingStateService, prisma);
  }

  async execute(data: PreSignupCreateAccountDto): Promise<PreSignupResponse> {
    // Get current context to merge with incoming data
    const cookie = this.cookieService.getCookie();
    if (!cookie?.stateSnapshot) {
      throw new Error('No state snapshot found');
    }

    // Create a temporary actor just to get the context
    const tempActor = this.createActor(cookie);
    tempActor.start();
    const context = tempActor.getSnapshot().context;
    tempActor.stop();

    // Create the submit event for account creation
    const event: OnboardingV1Events = {
      type: 'submit',
      subtype: 'createAccount',
      value: {
        email: data.email,
        password: data.password,
        phone: data.phone,
        firstName: context.firstName,
        lastName: context.lastName,
        getPromotionsSMS: data.getPromotionsSMS || false,
      },
    };

    return this.executeBase(event);
  }

  protected async validateStateRequirements(
    currentState: string,
    event: OnboardingV1Events,
  ): Promise<void> {
    // Account creation should only happen in the createAccount state
    if (!currentState.includes('createAccount')) {
      throw new ForbiddenException(
        `Account creation not allowed in state: ${currentState}`,
      );
    }

    // Validate the event has the required data
    if (
      event.type !== 'submit' ||
      event.subtype !== 'createAccount' ||
      !event.value?.email ||
      !event.value?.password ||
      !event.value?.phone ||
      !event.value?.firstName ||
      !event.value?.lastName
    ) {
      throw new ForbiddenException('Invalid account creation data');
    }
  }

  protected createActor(cookie: any) {
    const actors = {
      createUser: fromPromise<
        { success: boolean; authData?: any },
        { context: any }
      >(async ({ input }) => {
        const { context } = input;
        try {
          const headers = this.contextService.getRequestHeaders() || {};
          const authData = await this.patientSignUpUseCase.execute(
            {
              email: context.email,
              password: context.password,
              firstName: context.firstName,
              lastName: context.lastName,
              phone: context.phone,
              state: context.state,
              getPromotionsSMS: context.getPromotionsSMS,
            },
            headers,
            {} as any,
          );

          // Store auth tokens in the cookie
          this.cookieService.addAuthTokens({
            accessToken: authData.accessToken,
            refreshToken: authData.refreshToken,
            role: authData.role,
            patientId: authData.patientId,
          });

          // Store the patient ID in context so we can update the state later
          context.patientId = authData.patientId;

          // Return the complete PatientSignInOutput structure
          return { success: true, authData };
        } catch (error) {
          console.error('Error creating user:', error);
          return { success: false, authData: null };
        }
      }),
    };

    return super.createActor(cookie, actors);
  }
}
