import { OnboardingAttachDiscountUseCase } from '@modules/onboarding/use-cases/onboarding-attach-discount.use-case';
import { OnboardingBackUseCase } from '@modules/onboarding/use-cases/onboarding-back.use-case';
import { OnboardingCheckOnboardedUseCase } from '@modules/onboarding/use-cases/onboarding-check-onboarded.use-case.service';
import { OnboardingDesiredTreatmentTypeUseCase } from '@modules/onboarding/use-cases/onboarding-desired-treatment-type.use-case';
import { OnboardingDesiredTreatmentUseCase } from '@modules/onboarding/use-cases/onboarding-desired-treatment.use-case';
import { OnboardingFacePhotoUseCase } from '@modules/onboarding/use-cases/onboarding-face-photo.use-case';
import { OnboardingGetQuestionnaireStatusUseCase } from '@modules/onboarding/use-cases/onboarding-get-questionnaire-status.use-case';
import { OnboardingGetRecommendedTreatmentUseCase } from '@modules/onboarding/use-cases/onboarding-get-recommended-treatment.use-case';
import { OnboardingGetTreatmentTypeUseCase } from '@modules/onboarding/use-cases/onboarding-get-treatment-type-use-case.service';
import { OnboardingNextUseCase } from '@modules/onboarding/use-cases/onboarding-next.use-case';
import { OnboardingPreSignupBackUseCase } from '@modules/onboarding/use-cases/onboarding-pre-signup-back.use-case';
import { OnboardingPreSignupCreateAccountUseCase } from '@modules/onboarding/use-cases/onboarding-pre-signup-create-account.use-case';
import { OnboardingPreSignupNameUseCase } from '@modules/onboarding/use-cases/onboarding-pre-signup-name.use-case';
import { OnboardingPreSignupNextUseCase } from '@modules/onboarding/use-cases/onboarding-pre-signup-next.use-case';
import { OnboardingPreSignupStateUseCase } from '@modules/onboarding/use-cases/onboarding-pre-signup-state.use-case';
import { OnboardingPreSignupWaitingListUseCase } from '@modules/onboarding/use-cases/onboarding-pre-signup-waiting-list.use-case';
import { OnboardingQuestionnaireUseCase } from '@modules/onboarding/use-cases/onboarding-questionnaire.use-case';
import { OnboardingSetupPaymentIntentUseCase } from '@modules/onboarding/use-cases/onboarding-setup-payment-intent.use-case';
import { OnboardingShippingInfoUseCase } from '@modules/onboarding/use-cases/onboarding-shipping-info.use-case';
import { OnboardingStorePhotoUseCase } from '@modules/onboarding/use-cases/onboarding-store-photo-use.case';
import { OnboardingUpdateBillingAddressUseCase } from '@modules/onboarding/use-cases/onboarding-update-billing-address.use-case';
import { OnboardingValidateDiscountUseCase } from '@modules/onboarding/use-cases/onboarding-validate-discount.use-case';

import { OnboardingAttachedDiscountUseCase } from './onboarding-attached-discount.use-case';

export const OnboardingUseCases = [
  OnboardingQuestionnaireUseCase,
  OnboardingGetTreatmentTypeUseCase,
  OnboardingDesiredTreatmentUseCase,
  OnboardingDesiredTreatmentTypeUseCase,
  OnboardingStorePhotoUseCase,
  OnboardingFacePhotoUseCase,
  OnboardingShippingInfoUseCase,
  OnboardingSetupPaymentIntentUseCase,
  OnboardingGetQuestionnaireStatusUseCase,
  OnboardingGetRecommendedTreatmentUseCase,
  OnboardingUpdateBillingAddressUseCase,
  OnboardingBackUseCase,
  OnboardingNextUseCase,
  OnboardingCheckOnboardedUseCase,
  OnboardingAttachDiscountUseCase,
  OnboardingValidateDiscountUseCase,
  OnboardingAttachedDiscountUseCase,
  OnboardingPreSignupBackUseCase,
  OnboardingPreSignupNextUseCase,
  OnboardingPreSignupStateUseCase,
  OnboardingPreSignupNameUseCase,
  OnboardingPreSignupWaitingListUseCase,
  OnboardingPreSignupCreateAccountUseCase,
];
