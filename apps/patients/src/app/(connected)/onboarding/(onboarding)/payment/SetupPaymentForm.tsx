'use client';

import type { CheckoutInfo } from '@/hooks/onboarding';
import type {
  StripeCardCvcElement,
  StripeCardExpiryElement,
  StripeCardNumberElement,
  StripeCardNumberElementChangeEvent,
} from '@stripe/stripe-js';
import type React from 'react';
import { useEffect, useState } from 'react';
import Image from 'next/image';
import arrow from '@/assets/svg/arrow-circle.svg';
import { Amex } from '@/assets/svg/credit-card-logos/Amex';
import { Discover } from '@/assets/svg/credit-card-logos/Discover';
import { FSA } from '@/assets/svg/credit-card-logos/Fsa';
import { JCB } from '@/assets/svg/credit-card-logos/JCB';
import { Mastercard } from '@/assets/svg/credit-card-logos/Mastercard';
import { Visa } from '@/assets/svg/credit-card-logos/Visa';
import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { useAnalytics } from '@/context/AnalyticsContext';
import { useUpdateBilling } from '@/hooks/onboarding';
import { useAnalyticsData } from '@/hooks/useAnalyticsData';
import { useOnboardingNavigation } from '@/hooks/useOnboardingNavigation';
import { useStates } from '@/hooks/useStates';
import { getLongNamesByTypes, getshortNamesByTypes } from '@/lib/utils';
import { getFbValues } from '@/lib/utm-capture';
import { onboardingDataAtom } from '@/store/store';
import { zodResolver } from '@hookform/resolvers/zod';
import {
  CardCvcElement,
  CardExpiryElement,
  CardNumberElement,
  useElements,
  useStripe,
} from '@stripe/react-stripe-js';
import Big from 'big.js';
import classNames from 'classnames';
import { useAtom } from 'jotai/index';
import { useForm } from 'react-hook-form';
import { z } from 'zod';

import type { Treatment } from '../select-treatment/page';
import type { ShippingInfo } from '~/data/types';
import AddressAutoComplete from '../shipping/AddressAutoComplete';
import { ExpressCheckoutButton } from './ExpressCheckoutButton';

const schema = z
  .object({
    card: z.string().optional(),
    expiry: z.string().optional(),
    cvc: z.string().optional(),
    billingSameAsShipping: z.boolean().optional(),
    address1: z.string().min(1, { message: 'Address is required' }).optional(),
    address2: z.string().optional(),
    city: z.string().min(1, { message: 'City is required' }).optional(),
    state: z.string().optional(),
    zip: z.string().min(5, { message: 'Zip code is required' }).optional(),
  })
  .superRefine((data, context) => {
    if (!data.billingSameAsShipping) {
      if (!data.address1) {
        context.addIssue({
          message: 'Address is required',
          code: z.ZodIssueCode.custom,
          path: ['address1'],
        });
      }
      if (!data.city) {
        context.addIssue({
          message: 'City is required',
          code: z.ZodIssueCode.custom,
          path: ['city'],
        });
      }
      if (!data.state) {
        context.addIssue({
          message: 'State is required',
          code: z.ZodIssueCode.custom,
          path: ['state'],
        });
      }
      if (!data.zip) {
        context.addIssue({
          message: 'Zipcode code is required',
          code: z.ZodIssueCode.custom,
          path: ['zip'],
        });
      }
    }
    return true;
  });

type FormType = z.infer<typeof schema>;
type FormKeys = keyof z.infer<typeof schema>;

interface ElementStatus {
  name: FormKeys;
  error: any;
  complete: boolean;
}

const stripeInputStyle = {
  base: {
    backgroundColor: '#687999',
    color: '#fff',
    fontWeight: '500',
    fontSize: '20px',
    fontSmoothing: 'antialiased',
    '::placeholder': {
      color: '#fff',
    },
  },
};

export const OnboardingSetupPaymentForm = ({
  checkoutInfo,
}: {
  checkoutInfo: CheckoutInfo;
}) => {
  const analyticsData = useAnalyticsData();
  const stripe = useStripe();
  const [formError, setFormError] = useState<string | undefined>(undefined);
  const elements = useElements();
  const analytics = useAnalytics();

  const { data: states } = useStates();
  const { mutateAsync: updateBillingAddress } = useUpdateBilling('onboarding');

  const [cardStatus, setCardStatus] = useState<ElementStatus>({
    name: 'card',
    error: null,
    complete: false,
  });
  const [expiryStatus, setExpiryStatus] = useState<ElementStatus>({
    name: 'expiry',
    error: null,
    complete: false,
  });
  const [cvcStatus, setCvcStatus] = useState<ElementStatus>({
    name: 'cvc',
    error: null,
    complete: false,
  });

  const [isProcessing, setIsProcessing] = useState(false);
  const [onboarding] = useAtom(onboardingDataAtom);
  const { nextOnboardingStep } = useOnboardingNavigation();
  const [autoFillAddress, setAutoFillAddress] = useState<
    { address_components: any[]; name: string | undefined } | undefined
  >();

  const form = useForm<FormType>({
    resolver: zodResolver(schema),
    values: {
      billingSameAsShipping: true,
      address1: undefined,
      address2: undefined,
      city: undefined,
      state: undefined,
      zip: undefined,
    },
  });

  useEffect(() => {
    const types = [
      'locality',
      'administrative_area_level_1',
      'administrative_area_level_2',
      'postal_code',
    ];

    if (autoFillAddress) {
      const longNames = getLongNamesByTypes(
        autoFillAddress?.address_components,
        types,
      );
      const shortNames = getshortNamesByTypes(
        autoFillAddress?.address_components,
        ['administrative_area_level_1'],
      );
      form.setValue('address1', autoFillAddress?.name ?? '');
      form.setValue('city', longNames.locality ?? '');
      form.setValue('zip', longNames.postal_code ?? '');
      form.setValue('state', shortNames?.administrative_area_level_1 ?? '');
    }
  }, [autoFillAddress, form]);

  if (!elements || !states) return null;

  const cardNumber = elements.getElement(CardNumberElement) ?? null;

  const onStripeElementReady = (
    errorKey: FormKeys,
    element:
      | StripeCardNumberElement
      | StripeCardCvcElement
      | StripeCardExpiryElement,
    setter: React.Dispatch<React.SetStateAction<ElementStatus>>,
  ) => {
    (element as StripeCardNumberElement).on(
      'change',
      (e: StripeCardNumberElementChangeEvent) => {
        if (e.error) {
          form.setError(errorKey, { message: e.error.message });
        }
        if (!e.error) {
          form.clearErrors(errorKey);
        }
        setter((prev) => ({
          ...prev,
          error: e.error,
          complete: e.complete,
        }));
      },
    );
  };

  const nextStep = () => {
    if (onboarding) {
      nextOnboardingStep({
        ...onboarding,
        state: 'onboarded',
        percentage: 100,
        events: [],
      });

      const shipping = onboarding.context.shippingInfo;
      void emitCheckoutCompleteEvent(shipping);
    }
  };

  const getAnalyticsProducts = (treatments: Treatment[] | undefined) =>
    treatments?.map((treatment) => ({
      product_id: treatment.id,
      sku: treatment.id,
      name: treatment.name,
      price: treatment.price,
      image_url: treatment.image,
    }));

  const onSubmit = async (data: FormType) => {
    setFormError(undefined);
    if (!stripe || !elements) {
      return;
    }
    // manual validation of card form
    let isValid = true;

    for (const status of [cvcStatus, expiryStatus, cardStatus]) {
      if (!status.complete) {
        form.setError(status.name, { message: 'This field is required' });
        isValid = false;
      }
      if (status.error) {
        form.setError(status.name, { message: status.error.message });
        isValid = false;
      }
    }

    if (!isValid) return;

    setIsProcessing(true);

    // Only attempt to update billing if it's not an address suggestion and billing is different from shipping
    if (data.billingSameAsShipping === false) {
      try {
        await updateBillingAddress({
          address1: data?.address1 ?? '',
          address2: data?.address2 ?? '',
          city: data?.city ?? '',
          state: data?.state ?? '',
          zip: (data?.zip ?? '').toString(),
          force: true,
        });
      } catch (error) {
        setFormError((error as Error).message);
        setIsProcessing(false);
        return;
      }
    }

    const { error } = await stripe.confirmCardSetup(
      checkoutInfo.stripe.clientSecret,
      {
        payment_method: {
          card: cardNumber!,
        },
      },
    );

    if (error) {
      setIsProcessing(false);
      if (error.type === 'card_error' || error.type === 'validation_error') {
        // @todo create a FormMessage for id error to hold potential Stripe errors
        setFormError(error.message);
      } else {
        setFormError('An unexpected error occured.');
      }
      setIsProcessing(false);
      return;
    }

    nextStep();
    setIsProcessing(false);
  };

  const emitCheckoutCompleteEvent = (shipping: ShippingInfo | undefined) => {
    if (shipping === undefined) return;

    void analytics?.track('Checkout Complete', {
      ProductPrices:
        onboarding?.context.products?.map((product) => product.price) ?? [],
      ProductNames:
        onboarding?.context.products?.map((product) => product.name) ?? [],
      productIDs:
        onboarding?.context.products?.map((product) => product.id) ?? [],
      products: getAnalyticsProducts(onboarding?.context.products),
      shippingAddress1: shipping.address1 ?? shipping?.address1 ?? '',
      shippingAddress2: shipping.address2 ?? shipping?.address2 ?? '',
      shippingCity: shipping.city ?? shipping?.city ?? '',
      shippingState: shipping.state ?? shipping?.state ?? '',
      shippingZipcode: shipping.zip ?? shipping?.zip ?? '',
      value: checkoutInfo.totalDiscountedPrice,
      currency: 'USD',
      ...analyticsData,
    });

    const { fbc, fbp } = getFbValues();
    void analytics?.track('Onboarding Complete', {
      value: checkoutInfo.totalDiscountedPrice,
      ...analyticsData,
      state: shipping.state ?? shipping?.state ?? '',
      zipcode: shipping.zip ?? shipping?.zip ?? '',
      city: shipping.city ?? shipping?.city ?? '',
      currency: 'USD',
      fbc,
      fbp,
    });
  };

  const onPaymentSuccess = () => {
    setIsProcessing(true);
    const shipping = onboarding?.context.shippingInfo;
    emitCheckoutCompleteEvent(shipping);
    nextStep();
    setIsProcessing(false);
  };

  return (
    <div className="flex flex-col gap-10">
      <ExpressCheckoutButton
        expressCheckoutSecret={checkoutInfo.stripe.clientSecret}
        amountInCents={0}
        onPaymentSuccess={onPaymentSuccess}
      />
      <div className="flex w-full flex-col justify-between gap-5 md:flex-row">
        <div className="text-white">
          <div className="text-xl md:text-lg">Payment Method</div>
          <div className={classNames('text-base font-medium text-stone-300')}>
            Credit or Debit card
          </div>
        </div>

        <div className="flex gap-1">
          <Visa />
          <Mastercard />
          <Discover />
          <Amex />
          <JCB />
          <FSA />
        </div>
      </div>

      <Form {...form}>
        <form
          id="payment-form"
          onSubmit={form.handleSubmit(onSubmit)}
          className="flex w-full flex-col gap-5"
        >
          <div>
            <div
              className={classNames(
                'min-h-20 items-center rounded bg-glass p-5 text-xl',
              )}
            >
              <div className="pb-1 text-base font-medium text-stone-300">
                Card number
              </div>
              <CardNumberElement
                id="cardNumber"
                className="w-full"
                onReady={(element) =>
                  onStripeElementReady('card', element, setCardStatus)
                }
                options={{
                  placeholder: '0000 0000 0000 0000',
                  style: stripeInputStyle,
                }}
              />
            </div>
            <FormField
              control={form.control}
              name={'card'}
              render={() => (
                <FormItem>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>

          <div className="flex w-full flex-col gap-5 md:flex-row">
            <div className="w-full">
              <div
                className={classNames(
                  'flex h-20 items-center rounded bg-glass px-5 text-xl',
                )}
              >
                <CardExpiryElement
                  id="cardExpiry"
                  className="w-full"
                  onReady={(element) =>
                    onStripeElementReady('expiry', element, setExpiryStatus)
                  }
                  options={{
                    placeholder: 'MM/YY',
                    style: stripeInputStyle,
                  }}
                />
              </div>
              <FormField
                control={form.control}
                name={'expiry'}
                render={() => (
                  <FormItem>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
            <div className="w-full">
              <div className="flex h-20 items-center rounded bg-glass px-5 text-xl">
                <CardCvcElement
                  id="cardCvc"
                  className="w-full"
                  onReady={(element) =>
                    onStripeElementReady('cvc', element, setCvcStatus)
                  }
                  options={{ placeholder: 'CVC', style: stripeInputStyle }}
                />
              </div>
              <FormField
                control={form.control}
                name={'cvc'}
                render={() => (
                  <FormItem>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
          </div>

          <FormField
            control={form.control}
            name={'billingSameAsShipping'}
            render={({ field }) => (
              <FormItem>
                <FormControl>
                  <div className="my-5 flex items-center">
                    <Checkbox
                      id="set-billing-address"
                      checked={field.value}
                      onCheckedChange={(checked) => {
                        field.onChange(checked);
                        if (checked) {
                          form.setValue('address1', undefined);
                          form.setValue('address2', undefined);
                          form.setValue('city', undefined);
                          form.setValue('state', undefined);
                          form.setValue('zip', undefined);
                        }
                      }}
                      className="mr-4 h-5 w-5 data-[state=checked]:bg-electric"
                    />
                    <FormLabel
                      htmlFor="set-billing-address"
                      className="text-xl text-white"
                    >
                      My billing address is the same as my shipping address.
                    </FormLabel>
                  </div>
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          {!form.getValues('billingSameAsShipping') && (
            <div>
              <div className="mb-6 text-white">
                <div className="text-xl font-normal md:text-lg">
                  Billing Address
                </div>
              </div>

              <FormField
                control={form.control}
                name={'address1'}
                render={({ field }) => (
                  <FormItem>
                    <div className="grid w-full gap-1 py-2">
                      <div className="login-input relative flex w-full items-center">
                        <FormControl>
                          <AddressAutoComplete
                            setAutoFillAddress={setAutoFillAddress}
                          >
                            <Input
                              {...field}
                              type="text"
                              placeholder="Street address"
                              className="max-w-none"
                            />
                          </AddressAutoComplete>
                        </FormControl>
                      </div>
                      <FormMessage />
                    </div>
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name={'address2'}
                render={({ field }) => (
                  <FormItem>
                    <div className="grid w-full gap-1 py-2">
                      <div className="login-input relative flex w-full items-center">
                        <FormControl>
                          <Input
                            {...field}
                            type="text"
                            placeholder="Apartment/Suite"
                            className="max-w-none"
                          />
                        </FormControl>
                      </div>
                      <FormMessage />
                    </div>
                  </FormItem>
                )}
              />

              <div className="flex w-full flex-col md:flex-row md:gap-5">
                <FormField
                  control={form.control}
                  name={'city'}
                  render={({ field }) => (
                    <FormItem className="basis-full">
                      <div className="grid w-full gap-1 py-2">
                        <div className="login-input relative flex w-full items-center">
                          <FormControl>
                            <Input
                              {...field}
                              type="text"
                              placeholder="City"
                              className="max-w-none"
                            />
                          </FormControl>
                        </div>
                        <FormMessage />
                      </div>
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name={'state'}
                  render={({ field }) => (
                    <FormItem className="basis-full">
                      <div className="grid w-full gap-1 py-2">
                        <div className="login-input relative flex w-full items-center">
                          <FormControl>
                            <Select
                              onValueChange={field.onChange}
                              value={field.value}
                            >
                              <SelectTrigger className="max-w-none bg-glass py-6">
                                <SelectValue placeholder="Select your State" />
                              </SelectTrigger>
                              <SelectContent>
                                {states.map((state) => (
                                  <SelectItem
                                    key={state.code}
                                    value={state.code}
                                  >
                                    {state.name}
                                  </SelectItem>
                                ))}
                              </SelectContent>
                            </Select>
                          </FormControl>
                        </div>
                        <FormMessage />
                      </div>
                    </FormItem>
                  )}
                />
              </div>

              <FormField
                control={form.control}
                name={'zip'}
                render={({ field }) => (
                  <FormItem>
                    <div className="grid w-full gap-1 py-2">
                      <div className="login-input relative flex w-full items-center">
                        <FormControl>
                          <Input
                            {...field}
                            type="text"
                            placeholder="Zip code"
                            className="max-w-none"
                          />
                        </FormControl>
                      </div>
                      <FormMessage />
                    </div>
                  </FormItem>
                )}
              />
            </div>
          )}
          {formError && <div className="text-electric">{formError}</div>}

          <div className="flex items-center gap-10">
            <Button
              type="submit"
              size="lg"
              variant="electric"
              className="flex w-full max-w-none justify-between uppercase"
              disabled={isProcessing || !stripe || !elements}
            >
              <span>{isProcessing ? 'Submitting' : 'Continue'}</span>

              <Image alt="arrow" src={arrow} style={{ objectFit: 'contain' }} />
            </Button>
          </div>
        </form>
      </Form>
    </div>
  );
};
