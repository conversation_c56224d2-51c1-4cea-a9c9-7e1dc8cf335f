import type { OnboardingProps } from '@/app/(connected)/onboarding/(onboarding)/questionnaire/[slug]/page';
import React, { useState } from 'react';
import Image from 'next/image';
import arrow from '@/assets/svg/arrow-circle.svg';
import OnboardingTitle from '@/components/onboarding/OnboardingTitle';
import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormLoader,
  FormMessage,
} from '@/components/ui/form';
import { useOnboardingService } from '@/hooks/useOnboardingService';
import { cn } from '@/lib/utils';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import { z } from 'zod';

interface CheckboxOption {
  id: string;
  label: string;
}

interface CheckboxListQuestionConfig {
  fieldName: string;
  title: string;
  subtitle?: string;
  options: CheckboxOption[];
  noneOption?: CheckboxOption;
  noneSpacing?: boolean;
  errorMessage?: string;
  getContextValue: (context: any) => string[];
}

interface CheckboxListQuestionProps extends OnboardingProps {
  config: CheckboxListQuestionConfig;
}

const CheckboxListQuestion = ({
  data,
  callback,
  config,
}: CheckboxListQuestionProps) => {
  const {
    fieldName,
    title,
    subtitle,
    options,
    noneOption,
    noneSpacing = true,
    errorMessage = 'Please select at least one option',
    getContextValue,
  } = config;

  const [isLoading, setIsLoading] = useState(false);

  // Combine regular options with none option if provided
  const allOptions = noneOption ? [...options, noneOption] : options;
  const noneId = noneOption?.id;

  const schema = z.object({
    [fieldName]: z.array(z.string()).min(1, errorMessage),
  });

  type FormType = z.infer<typeof schema>;

  // Initialize form with existing data
  const existingValues = getContextValue(data.context) || [];

  const form = useForm<FormType>({
    resolver: zodResolver(schema),
    values: {
      [fieldName]: existingValues.length > 0 ? existingValues : [],
    } as FormType,
  });

  const { nextQuestionnaire, isLoading: isTransitioning } =
    useOnboardingService();

  const onSubmit = async (formData: FormType) => {
    try {
      setIsLoading(true);

      let values = formData[fieldName];
      // If NONE is selected, clear any other selections
      if (noneId && values.includes(noneId)) {
        values = [];
      }

      const response = await nextQuestionnaire({ [fieldName]: values });
      callback(response.data);
    } catch (e: any) {
      setIsLoading(false);
      form.setError(fieldName as any, {
        message: e.response?.data?.message,
      });
    }
  };

  // Check if any option is selected
  const hasSelection = form.watch(fieldName as any)?.length > 0;

  return (
    <OnboardingTitle title={title} subtitle={subtitle}>
      <Form {...form}>
        <form
          onSubmit={form.handleSubmit(onSubmit)}
          className="flex flex-col gap-10"
        >
          <FormLoader isLoading={isLoading || isTransitioning}>
            <div className="gap-5">
              <FormField
                control={form.control}
                name={fieldName as any}
                render={() => (
                  <FormItem>
                    {allOptions.map((item, index) => (
                      <React.Fragment key={item.id}>
                        {/* Add spacing before the none option */}
                        {noneOption &&
                          noneSpacing &&
                          index === allOptions.length - 1 && (
                            <div className="h-12" />
                          )}
                        <FormField
                          control={form.control}
                          name={fieldName as any}
                          render={({ field }) => (
                            <FormItem
                              className={cn(
                                'medical',
                                field.value?.includes(item.id) &&
                                  'bg-[#445f85]',
                              )}
                            >
                              <FormControl>
                                <div
                                  className={cn(
                                    'rounded border border-[#63799A] p-5',
                                  )}
                                >
                                  <FormLabel
                                    htmlFor={`${fieldName}-${item.id}`}
                                    className="flex items-center text-lg text-white"
                                  >
                                    <Checkbox
                                      id={`${fieldName}-${item.id}`}
                                      checked={field.value?.includes(item.id)}
                                      onCheckedChange={(checked) => {
                                        if (checked) {
                                          // If NONE is selected, clear all other selections
                                          if (noneId && item.id === noneId) {
                                            return field.onChange([noneId]);
                                          }
                                          // If any other option is selected, remove NONE if it's selected
                                          const newValue = [
                                            ...field.value,
                                            item.id,
                                          ].filter((value) => value !== noneId);
                                          return field.onChange(newValue);
                                        } else {
                                          return field.onChange(
                                            field.value?.filter(
                                              (value) => value !== item.id,
                                            ),
                                          );
                                        }
                                      }}
                                      className="mr-4 data-[state=checked]:bg-electric"
                                    />
                                    {item.label}
                                  </FormLabel>
                                </div>
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </React.Fragment>
                    ))}
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <Button
              size="lg"
              variant="electric"
              className="flex w-full max-w-none justify-between"
              type="submit"
              disabled={isTransitioning || !hasSelection}
            >
              <span>CONTINUE</span>
              <Image alt="arrow" src={arrow} style={{ objectFit: 'contain' }} />
            </Button>
          </FormLoader>
        </form>
      </Form>
    </OnboardingTitle>
  );
};

export default CheckboxListQuestion;
