import { Injectable, Logger } from '@nestjs/common';

import type { OnboardingV1Events } from '../states/versions/onboarding-v1.types';
import {
  OnboardingPreSignupBaseUseCase,
  PreSignupResponse,
} from './onboarding-pre-signup-base.use-case';

@Injectable()
export class OnboardingPreSignupNextUseCase extends OnboardingPreSignupBaseUseCase {
  protected readonly logger = new Logger(OnboardingPreSignupNextUseCase.name);

  async execute(): Promise<PreSignupResponse> {
    // Create the next event without any data
    const event: OnboardingV1Events = {
      type: 'next',
    };

    return this.executeBase(event);
  }

  // No validation needed - generic next transitions are allowed
  // The state machine will handle whether the transition is valid
}
