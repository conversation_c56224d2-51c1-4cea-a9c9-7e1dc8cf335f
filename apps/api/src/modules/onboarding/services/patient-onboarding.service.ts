import {
  PatientOnboardingProfile,
  PatientPersistence,
} from '@adapters/persistence/database/patient.persistence';
import { OnboardingUpdatedEvent } from '@modules/onboarding/events/onboarding-updated.event';
import { UtmService } from '@modules/shared/services/utm.service';
import { CACHE_MANAGER } from '@nestjs/cache-manager';
import { Inject, Injectable } from '@nestjs/common';
import { OnEvent } from '@nestjs/event-emitter';
import { Cache } from 'cache-manager';
import { Request } from 'express';

@Injectable()
export class PatientOnboardingService {
  // private state: Actor;
  constructor(
    private readonly patientPersistence: PatientPersistence,
    @Inject(CACHE_MANAGER)
    private cacheService: Cache,
    private readonly utmService: UtmService,
  ) {}

  async getPatientData(req: Request): Promise<PatientOnboardingProfile> {
    const cognitoId = req.user['userId'];
    if (!cognitoId) throw new Error('Missing cognitoId in request');

    // if there is a patient cookie, decrypt it and return the patient data
    // if (
    //   req.signedCookies?.['patient'] &&
    //   req.signedCookies?.['patient'] !== 'null'
    // ) {
    //   try {
    //     const profile = JSON.parse(
    //       req.signedCookies['patient'],
    //     ) as PatientProfile;
    //     if (profile.id !== cognitoId) {
    //       throw new Error('Invalid patient cookie');
    //     }
    //     return profile;
    //   } catch (e) {
    //     req.res.clearCookie('patient');
    //     await this.cacheService.del(`patient:${cognitoId}`);
    //     throw new Error('Invalid patient cookie');
    //   }
    // }

    // if there is no patient cookie, get the patient data from the cache
    // const cachedPatient = await this.cacheService.get<PatientProfile>(
    //   `patient:${cognitoId}`,
    // );
    //
    // if (cachedPatient) {
    //   // set the patient cookie
    //   req.res.cookie('patient', JSON.stringify(cachedPatient), {
    //     httpOnly: true,
    //     signed: true,
    //   });
    //
    //   return cachedPatient;
    // }

    // if there is no patient data in the cache, get it from the database
    const patient =
      await this.patientPersistence.getOnboardingProfile(cognitoId);
    if (!patient)
      throw new Error(`Patient not found for username: ${cognitoId}`);

    await this.utmService.setupAndStoreCampaignDataToCache(
      req.headers,
      patient.id,
    );
    // set the patient cookie
    // @todo set proper expiration time
    req.res.cookie('patient', JSON.stringify(patient), {
      httpOnly: true,
      signed: true,
    });

    return patient;
  }
  // @todo might want to update the cached data and cookie, but for now we'll re-fetch it
  // async updateOnboardingSession(req: Request, data: Partial<PatientProfile>) {}

  @OnEvent('onboarding.updated')
  async updateOnboardingPatientData(event: OnboardingUpdatedEvent) {
    return this.patientPersistence.update(event.patientId, event.data);
  }
}
