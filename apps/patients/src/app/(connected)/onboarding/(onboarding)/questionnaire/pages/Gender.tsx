import type { OnboardingProps } from '@/app/(connected)/onboarding/(onboarding)/questionnaire/[slug]/page';
import RadioGroupQuestion from '@/components/onboarding/RadioGroupQuestion';

const Gender = (props: OnboardingProps) => {
  const config = {
    fieldName: 'gender',
    title: 'What was the sex assigned to you at birth?',
    options: [
      { value: 'male', label: 'Male' },
      { value: 'female', label: 'Female' },
    ],
    schemaValues: ['male', 'female'] as [string, string],
    errorMessage: 'Please select your gender',
    getContextValue: (context: any) => context.questionnaire.gender,
    updateAtom: (gender: string) => ({ gender }),
  };

  return <RadioGroupQuestion {...props} config={config} />;
};

export default Gender;
