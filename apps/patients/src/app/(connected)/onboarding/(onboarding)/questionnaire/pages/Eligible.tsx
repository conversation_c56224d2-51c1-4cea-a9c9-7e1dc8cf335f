import type { OnboardingProps } from '@/app/(connected)/onboarding/(onboarding)/questionnaire/[slug]/page';
import SimpleInfoPage from '@/components/onboarding/SimpleInfoPage';
import { useAnalytics } from '@/context/AnalyticsContext';
import { useAnalyticsData } from '@/hooks/useAnalyticsData';

const Eligible = (props: OnboardingProps) => {
  const analyticsData = useAnalyticsData();
  const analytics = useAnalytics();

  const config = {
    title: 'Great! You may be eligible for treatment.',
    subtitle: `Let's get started with your doctor visit. All the information you provide is required to provide safe and appropriate medical care, and is encrypted with our secure platform.`,
    onBeforeSubmit: () => {
      void analytics?.track('Visit Started', analyticsData);
    },
  };

  return <SimpleInfoPage {...props} config={config} />;
};

export default Eligible;
