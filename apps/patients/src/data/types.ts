import type { patientStatus, patientVerificationStatus } from '@prisma/client';

import type { IdPhotoRejectionReason } from '@willow/utils/patient/id-photo';

import type { Treatment } from '~/app/(connected)/onboarding/(onboarding)/select-treatment/page';

export interface Patient {
  id: string;
  stripeCustomerId: string;
  idPhoto: any;
  facePhoto: any;
  status: string;
  statusBeforeCancellation?: string;
  verificationStatus: string;
  gender: string;
  state: State;
  user: User;
  doctor?: any;
  paymentMethods: PaymentMethod[];
  shippingAddresses: ShippingAddress[];
  prescriptions: any[];
}

export interface DashboardData {
  createdAt: string;
  id: string;
  stripeCustomerId: string;
  idPhoto: string;
  facePhoto: string;
  status: patientStatus;
  statusBeforeCancellation?: string;
  gender: 'male' | 'female';
  birthDate: string;
  verificationStatus: patientVerificationStatus;
  rejectedStatus: IdPhotoRejectionReason;
  rejectedReason: string;
  state: State;
  user: User;
  doctor: Doctor;
  referralCode: string;
  paymentMethods: PaymentMethod[];
  shippingAddresses: ShippingAddress[];
  treatment?: {
    name: string;
    form: 'oral' | 'injectable';
    dose: string;
    dosageDescription: string;
    dosageTimeframe: string;
    syringe?: {
      maxUnits: number;
      currentUnits: number;
      step: number;
    };
  };
  conversation?: Conversation;
  paymentMethod: PaymentMethod;
}

export interface StatusData {
  onboarding: OnboardingData;
  getStarted: GetStarted;
  promoCoupon?: Coupon;
}

export interface State {
  id: string;
  name: string;
  code: string;
  enabled: boolean;
}

export interface Coupon {
  id: string;
  valid: boolean;
  percentOff: number;
}

export interface User {
  id: string;
  type: string;
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
}

export interface Doctor {
  id: string;
  image: string;
  user: User;
}

export interface BillingAddress {
  city: string;
  country: string;
  line1: string;
  line2: string;
  postal_code: string;
  state: string;
}

export interface ShippingAddress {
  id: string;
  patientId: string;
  address1: string;
  address2?: string;
  city: string;
  stateId: string;
  state: State;
  zip: string;
  default: boolean;
  createdAt: string;
}

export type ShippingAddressUpdate = Omit<
  ShippingAddress,
  'id' | 'stateId' | 'state' | 'default' | 'patientId' | 'createdAt'
> & { state: string; force?: boolean };

export interface ShippingInfo {
  address1: string;
  address2?: string;
  city: string;
  state: string;
  zip: string;
  force?: boolean;
}

export interface PaymentMethod {
  id: string;
  stripeId: string;
  patientId: string;
  type: string;
  data: Data;
  default: boolean;
  createdAt: string;
}

export interface Data {
  id: string;
  card: Card;
  type: string;
  object: string;
  created: number;
  customer: string;
  livemode: boolean;
  billing_details: BillingDetails;
}

export interface Card {
  brand: string;
  last4: string;
  country: string;
  exp_year: number;
  exp_month: number;
}

export interface BillingDetails {
  name: any;
  email: any;
  phone: any;
  address: Address;
}

export interface Address {
  city: any;
  line1: any;
  line2: any;
  state: any;
  country: any;
  postal_code: any;
}

export interface Conversation {
  id: string;
  userId: string;
  patientId: string;
  status: string;
  type: string;
  lastMessageText: any;
  lastMessageFrom: any;
  createdAt: string;
  updatedAt: string;
  messages?: Message[];
  watcher: Watcher[];
}

export interface Message {
  id: string;
  conversationId: string;
  userId: string;
  type: 'message' | 'system';
  contentType: 'text' | 'image' | 'file';
  content: string;
  createdAt: string;
  user: User;
}

export interface ElegibleCancelationDiscount {
  isEligible: boolean;
}

export interface Watcher {
  unreadMessages: number;
  updatedAt: string | null;
}

export interface OnboardingSteps {
  back?: string;
  stepName: string;
  percentage: number;
}

export interface SignInResponse {
  accessToken: string;
  refreshToken: string;
  idToken: string;
  role: string;
  status: string;
  patientId: string;
  onboarding?: OnboardingData;
  dashboard?: DashboardData;
  getStarted: GetStarted;
  impersonated?: boolean;
}

export interface GetStarted {
  state?: string;
  firstName?: string;
  lastName?: string;
  email?: string;
  phone?: string;
  gender?: string;
  birthday?: string;
}

export interface OnboardingData {
  state: string | { [key: string]: string; questionnaire: string };
  context: {
    productType: string;
    questionnaireCompleted: boolean;
    questionnaire: Record<string, any>;
    products?: Treatment[];
    'id-photo'?: string;
    'face-photo'?: string;
    shippingInfo?: ShippingInfo;
    paymentIntent?: string;
    firstName?: string;
    lastName?: string;
    state?: string;
    email?: string;
    phone?: string;
  };
  events: string[];
  stepName: string;
  percentage: number;
}

export interface Notification {
  message: string;
}

export interface TreatmentType {
  image: string;
  id: string;
  name: string;
  tags: string[];
  basePrice: number;
  shortDescription: string;
  description: string;
  form?: string | null;
  customCard?: string | null;
}

export type YesNo = 'yes' | 'no';

export interface FollowUpData {
  hasInProgressFollowUp: boolean;
  scheduledAt: string;
  events: string[];
  increasePrice: {
    name: string;
    next: number;
    doses: string[];
  } | null;
  totalSteps: number;
  currentStep: {
    name: string;
    number: number;
  };
  context: {
    questionnaire: {
      startWeight?: number;
      currentWeight?: number;
      goalWeight?: number;
      areThereMedicalHistoryChanges?: YesNo;
      medicalHistoryChanges?: string;
      areThereMedicationSideEffects?: YesNo;
      medicationSideEffects?: string;
      isContinueMedication?: YesNo;
      stopMedicationReason?: string;
      isContinueCurrentDose: YesNo;
      isIncreaseSupply: YesNo;
      changeDoseReason?: string;
      questions?: string;
      moreInfo?: string;
    };
    completedAt?: string;
    questionnaireCompleted: boolean;
  };
}

export interface IrClickId {
  irclickid: string;
  date: Date;
}
