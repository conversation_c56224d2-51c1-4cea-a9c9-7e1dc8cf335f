'use client';

import type { OnboardingData } from '@/data/types';
import { useEffect, useState } from 'react';
import DynamicRenderer from '@/components/onboarding/DynamicRenderer';
import { useAnalytics } from '@/context/AnalyticsContext';
import { useAnalyticsData } from '@/hooks/useAnalyticsData';
import { useOnboardingNavigation } from '@/hooks/useOnboardingNavigation';
import { onboardingStepsAtom } from '@/store/store';
import { useSetAtom } from 'jotai';

export interface OnboardingProps {
  data: OnboardingData;
  callback: (response: OnboardingData) => void;
}

const Questionnaire = () => {
  const analyticsData = useAnalyticsData();
  const { onboarding, nextOnboardingStep } = useOnboardingNavigation();
  const [componentName, setComponentName] = useState<string | null>(null);
  const setSteps = useSetAtom(onboardingStepsAtom);
  const analytics = useAnalytics();

  // update dynamic component
  useEffect(() => {
    if (!onboarding) return;
    if (
      typeof onboarding.state !== 'string' &&
      onboarding.state.questionnaire
    ) {
      setComponentName(onboarding.state.questionnaire);
    } else {
      void analyticsQuestionnarieCompleted();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [onboarding]);

  const analyticsQuestionnarieCompleted = () => {
    if (!analytics) return;
    void analytics?.track('Questionnaire Completed', analyticsData);
  };

  // update step counter
  useEffect(() => {
    if (!onboarding) return;
    const { stepName, percentage } = onboarding;
    setSteps({ stepName, percentage });
  }, [onboarding, setSteps]);

  const callback = (response: OnboardingData) => {
    nextOnboardingStep(response);
  };

  if (!componentName || !onboarding) return;

  return (
    <DynamicRenderer
      componentName={componentName}
      data={onboarding}
      callback={callback}
    />
  );
};

export default Questionnaire;
