'use client';

import React, { useEffect } from 'react';
import Image from 'next/image';
import arrow from '@/assets/svg/arrow-circle.svg';
import OnboardingTitle from '@/components/onboarding/OnboardingTitle';
import { Button } from '@/components/ui/button';
import { Form } from '@/components/ui/form';
import InputField from '@/components/ui/InputField';
import { useOnboardingNavigation } from '@/hooks/useOnboardingNavigation';
import { useOnboardingService } from '@/hooks/useOnboardingService';
import { onboardingDataAtom, onboardingStepsAtom } from '@/store/store';
import { zodResolver } from '@hookform/resolvers/zod';
import { useAtomValue, useSetAtom } from 'jotai';
import { useForm } from 'react-hook-form';
import { z } from 'zod';

const schema = z.object({
  firstName: z.string().min(1, { message: 'First name is required' }),
  lastName: z.string().min(1, { message: 'Last name is required' }),
});

const NameForm = () => {
  const setSteps = useSetAtom(onboardingStepsAtom);
  const onboardingData = useAtomValue(onboardingDataAtom);
  const { preSignupName } = useOnboardingService();
  const { nextOnboardingStep } = useOnboardingNavigation();

  const form = useForm<z.infer<typeof schema>>({
    resolver: zodResolver(schema),
    defaultValues: {
      firstName: onboardingData?.context?.firstName || '',
      lastName: onboardingData?.context?.lastName || '',
    },
  });

  useEffect(() => {
    setSteps({
      stepName: 'Account Creation',
      percentage: 0, // Will be updated by API
      back: '/account/state',
    });
  }, [setSteps]);

  const onSubmit = async (values: z.infer<typeof schema>) => {
    try {
      const response = await preSignupName.mutateAsync(values);

      // Update the onboarding data with the response
      nextOnboardingStep({
        state: response.currentState,
        context: response.context || {},
        events: [],
        stepName: response.stepName || 'Account Creation',
        percentage: response.percentage || 0, // Backend always calculates
      });
    } catch (error) {
      console.error('Failed to submit email and name:', error);
    }
  };

  return (
    <OnboardingTitle
      title="Let's start with your name"
      subtitle="We'll use this information to help create your account and personalize your experience with Willow."
    >
      <Form {...form}>
        <form
          onSubmit={form.handleSubmit(onSubmit)}
          className="flex w-full flex-col gap-10"
        >
          <div className="flex flex-col gap-5">
            <InputField
              control={form.control}
              name="firstName"
              type="text"
              placeholder="First name"
              variant="dark"
            />

            <InputField
              control={form.control}
              name="lastName"
              type="text"
              placeholder="Last name"
              variant="dark"
            />
          </div>

          <Button
            type="submit"
            size="lg"
            variant="electric"
            className="flex w-full max-w-none justify-between"
          >
            <span>CONTINUE</span>
            <Image alt="arrow" src={arrow} style={{ objectFit: 'contain' }} />
          </Button>
        </form>
      </Form>
    </OnboardingTitle>
  );
};

export default NameForm;
