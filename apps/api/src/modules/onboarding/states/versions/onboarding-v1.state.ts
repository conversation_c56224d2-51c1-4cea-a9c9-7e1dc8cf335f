import * as dayjs from 'dayjs';
import { assign, fromPromise, setup } from 'xstate';

import type {
  AddToWaitingListInput,
  AddToWaitingListOutput,
  CheckStateEnabledInput,
  CheckStateEnabledOutput,
  CreateUserInput,
  CreateUserOutput,
  OnboardingV1Context,
  OnboardingV1Events,
  OnboardingV1Input,
  PostQuestionnaireData,
  PreSignupData,
  QuestionnaireStepData,
  ValidateNameInput,
  ValidateNameOutput,
} from './onboarding-v1.types';

export const onboardingV1Machine = setup({
  types: {
    context: {} as OnboardingV1Context,
    events: {} as OnboardingV1Events,
    input: {} as OnboardingV1Input,
  },
  actors: {
    // This will be provided by the use case when creating the actor
    checkStateEnabled: fromPromise<
      CheckStateEnabledOutput,
      CheckStateEnabledInput
    >(async () => {
      throw new Error('checkStateEnabled actor must be provided at runtime');
    }),
    createUser: fromPromise<CreateUserOutput, CreateUserInput>(async () => {
      throw new Error('createUser actor must be provided at runtime');
    }),
    addToWaitingList: fromPromise<
      AddToWaitingListOutput,
      AddToWaitingListInput
    >(async () => {
      throw new Error('addToWaitingList actor must be provided at runtime');
    }),
    validateName: fromPromise<ValidateNameOutput, ValidateNameInput>(
      async () => {
        throw new Error('validateName actor must be provided at runtime');
      },
    ),
  },
  guards: {
    isUnderAge: function ({ event }) {
      if (event.type !== 'next' || !event.value) return false;
      const data = event.value as QuestionnaireStepData;
      if (!data.birthDate) return false;
      const birthDate = dayjs(data.birthDate);
      const eighteenYearsAgo = dayjs().subtract(18, 'year');

      return birthDate.isAfter(eighteenYearsAgo);
    },

    isOverAge: function ({ event }) {
      if (event.type !== 'next' || !event.value) return false;
      const data = event.value as QuestionnaireStepData;
      if (!data.birthDate) return false;
      const birthDate = dayjs(data.birthDate);
      const seventyFiveYearsAgo = dayjs().subtract(75, 'year');

      return birthDate.isBefore(seventyFiveYearsAgo);
    },
    verifyPriorConditions: function ({ event }) {
      if (event.type !== 'next' || !event.value) return false;
      const data = event.value as QuestionnaireStepData;
      if (!data.priorConditions) return false;
      return data.priorConditions.length === 0;
    },
  },
  actions: {
    reject: assign((_, params: { reason: string }) => ({
      rejected: true,
      rejectedReason: params.reason,
    })),
    clearRejected: assign(() => {
      return { rejected: false, rejectedReason: undefined };
    }),
    complete: assign(() => {
      return { questionnaireCompleted: true };
    }),
    storeQuestionnaire: assign(({ context, event }, params) => {
      // For 'next' events, merge the event value data
      const data =
        event.type === 'next' && event.value
          ? (event.value as QuestionnaireStepData)
          : {};

      // Always merge params (for radio button selections) and data
      context.questionnaire = {
        ...context.questionnaire,
        ...data,
        ...params,
      };
      return context;
    }),
    store: assign(({ context, event }) => {
      if (event.type !== 'next') return context;
      const data = event.value as PostQuestionnaireData;
      return { ...context, ...data };
    }),
    storeClientSecret: assign(({ context, event }) => {
      if (event.type !== 'next' || !event.value) return context;
      const data = event.value as PostQuestionnaireData;
      if (!data.paymentIntent) return context;
      return { ...context, paymentIntent: data.paymentIntent };
    }),
    storePreSignup: assign(({ context, event }) => {
      if (event.type === 'next' && event.value) {
        const data = event.value as PreSignupData;
        return { ...context, ...data };
      }

      if (event.type === 'submit') {
        return { ...context, ...event.value };
      }

      return context;
    }),
  },
}).createMachine({
  context: ({ input }) => ({
    questionnaireCompleted: false,
    questionnaire: {},
    ...(input?.existingData || {}),
  }),
  id: 'onboarding-v1',
  initial: 'preSignup',
  meta: {
    version: 'v1',
    total: 4 + 15 + 9, // 4 pre-signup + 15 questionnaire + 9 post-questionnaire
  },
  states: {
    preSignup: {
      initial: 'stateSelection',
      states: {
        stateSelection: {
          on: {
            next: {
              target: 'checkingState',
              actions: 'storePreSignup',
            },
          },
          meta: { step: 1, name: 'Account Creation' },
        },
        checkingState: {
          invoke: {
            src: 'checkStateEnabled',
            input: ({ context }) => ({
              stateCode: context.state,
            }),
            onDone: {
              target: 'firstAndLastName',
              actions: assign(({ event }) => ({
                state: event.output.stateCode,
              })),
            },
            onError: {
              target: 'unsupportedState',
              actions: ({ event }) => {
                console.error('State not supported:', event.error);
              },
            },
          },
          meta: { step: 1, name: 'Account Creation' },
        },
        firstAndLastName: {
          on: {
            submit: {
              target: 'validatingName',
              actions: 'storePreSignup',
            },
            back: {
              target: 'stateSelection',
            },
          },
          meta: { step: 2, name: 'Account Creation' },
        },
        validatingName: {
          invoke: {
            src: 'validateName',
            input: ({ context }) => ({
              firstName: context.firstName || '',
              lastName: context.lastName || '',
            }),
            onDone: {
              target: 'createAccount',
            },
            onError: {
              target: 'firstAndLastName',
              actions: ({ event }) => {
                console.error('Error validating name:', event.error);
              },
            },
          },
          meta: { step: 2, name: 'Account Creation' },
        },
        createAccount: {
          on: {
            submit: {
              target: 'creatingUser',
              actions: 'storePreSignup',
            },
            back: {
              target: 'firstAndLastName',
            },
          },
          meta: { step: 3, name: 'Account Creation' },
        },
        creatingUser: {
          invoke: {
            src: 'createUser',
            input: ({ context }) => ({ context }),
            onDone: {
              target: '#onboarding-v1.questionnaire',
              actions: assign(({ event }) => {
                const output = event.output as {
                  success: boolean;
                  authData?: any;
                };
                return { authData: output.authData };
              }),
            },
            onError: {
              target: 'createAccount',
              actions: ({ event }) => {
                console.error('Error creating user:', event.error);
              },
            },
          },
          meta: { step: 3, name: 'Account Creation' },
        },
        unsupportedState: {
          on: {
            submit: {
              target: 'addingToWaitingList',
            },
            back: {
              target: 'stateSelection',
              actions: 'clearRejected',
            },
          },
          meta: { step: 2, name: 'Account Creation' },
        },
        addingToWaitingList: {
          invoke: {
            src: 'addToWaitingList',
            input: ({ context, event }) => ({
              email:
                event.type === 'submit' && event.subtype === 'waitingList'
                  ? event.value.email
                  : context.email,
              state: context.state,
            }),
            onDone: {
              target: 'unsupportedStateThankYou',
            },
            onError: {
              target: 'unsupportedState',
              actions: ({ event }) => {
                console.error('Error adding to waiting list:', event.error);
              },
            },
          },
          meta: { step: 3, name: 'Account Creation' },
        },
        unsupportedStateThankYou: {
          on: {
            back: {
              target: 'stateSelection',
            },
          },
          meta: { step: 4, name: 'Account Creation' },
        },
      },
    },
    questionnaire: {
      // Insert extracted questionnaire config here
      meta: {
        total: 15,
      },
      initial: 'age',
      states: {
        age: {
          on: {
            next: [
              {
                guard: 'isUnderAge',
                target: 'rejectedUnderAge',
              },
              {
                guard: 'isOverAge',
                target: 'rejectedOverAge',
              },
              {
                target: 'gender',
                actions: 'storeQuestionnaire',
              },
            ],
          },
          meta: {
            step: 5, // Offset by 4 for pre-signup steps
          },
        },
        gender: {
          on: {
            back: {
              target: 'age',
            },
            male: {
              target: 'usingGLP1',
              actions: {
                type: 'storeQuestionnaire',
                params: {
                  gender: 'male',
                },
              },
            },
            female: {
              target: 'isPregnant',
              actions: {
                type: 'storeQuestionnaire',
                params: {
                  gender: 'female',
                },
              },
            },
          },
          meta: {
            step: 7,
          },
        },
        height: {
          on: {
            back: {
              target: 'qualifyingConditions',
            },
            next: {
              target: 'weight',
              actions: 'storeQuestionnaire',
            },
          },
          meta: {
            step: 11,
          },
        },
        weight: {
          on: {
            back: {
              target: 'height',
            },
            next: {
              target: 'desiredWeight',
              actions: 'storeQuestionnaire',
            },
          },
          meta: {
            step: 12,
          },
        },
        eligible: {
          on: {
            back: {
              target: 'haveDiabetes',
            },
            next: {
              target: 'doctorVisits',
            },
          },
          meta: {
            step: 8,
          },
        },
        finished: {
          entry: {
            type: 'complete',
            params: {
              completed: true,
            },
          },
          always: '#onboarding-v1.selectTreatmentType',
        },
        usingGLP1: {
          on: {
            no: {
              target: 'haveDiabetes',
              actions: {
                type: 'storeQuestionnaire',
                params: {
                  usingGLP1: 'no',
                },
              },
            },
            yes: {
              target: 'haveDiabetes',
              actions: {
                type: 'storeQuestionnaire',
                params: {
                  usingGLP1: 'yes',
                },
              },
            },
            back: {
              target: 'gender',
            },
          },
          meta: {
            step: 7,
          },
        },
        isPregnant: {
          on: {
            no: {
              target: 'usingGLP1',
              actions: {
                type: 'storeQuestionnaire',
                params: {
                  isPregnant: 'no',
                },
              },
            },
            yes: {
              target: 'rejectedIsPregnant',
              actions: {
                type: 'storeQuestionnaire',
                params: {
                  isPregnant: 'yes',
                },
              },
            },
            back: {
              target: 'gender',
            },
          },
          meta: {
            step: 7,
          },
        },
        objectives: {
          on: {
            back: {
              target: 'desiredWeight',
            },
            next: {
              target: 'haveAllergies',
              actions: 'storeQuestionnaire',
            },
          },
          meta: {
            step: 14,
          },
        },
        medications: {
          on: {
            back: {
              target: 'haveAllergies',
            },
            next: {
              target: 'medicalConditions',
              actions: 'storeQuestionnaire',
            },
          },
          meta: {
            step: 16,
          },
        },
        doctorVisits: {
          on: {
            no: {
              target: 'recommendSeeDoctor',
              actions: {
                type: 'storeQuestionnaire',
                params: {
                  doctorVisits: 'no',
                },
              },
            },
            yes: {
              target: 'qualifyingConditions',
              actions: {
                type: 'storeQuestionnaire',
                params: {
                  doctorVisits: 'yes',
                },
              },
            },
            back: {
              target: 'eligible',
            },
          },
          meta: {
            step: 9,
          },
        },
        haveDiabetes: {
          on: {
            no: {
              target: 'eligible',
              actions: {
                type: 'storeQuestionnaire',
                params: {
                  haveDiabetes: 'no',
                },
              },
            },
            yes: {
              target: 'rejectedPriorConditions',
              actions: {
                type: 'storeQuestionnaire',
                params: {
                  haveDiabetes: 'yes',
                },
              },
            },
            back: {
              target: 'usingGLP1',
            },
          },
          meta: {
            step: 7,
          },
        },
        desiredWeight: {
          on: {
            back: {
              target: 'weight',
            },
            next: {
              target: 'objectives',
              actions: 'storeQuestionnaire',
            },
          },
          meta: {
            step: 13,
          },
        },
        haveAllergies: {
          on: {
            no: {
              target: 'medications',
              actions: {
                type: 'storeQuestionnaire',
                params: {
                  allergies: [],
                  hasAllergies: 'no',
                },
              },
            },
            yes: {
              target: 'selectAllergies',
              actions: {
                type: 'storeQuestionnaire',
                params: {
                  hasAllergies: 'yes',
                },
              },
            },
            back: {
              target: 'objectives',
            },
          },
          meta: {
            step: 15,
          },
        },
        rejectedOverAge: {
          on: {
            back: {
              target: 'age',
              actions: 'clearRejected',
            },
          },
          meta: {
            step: 5,
          },
          entry: {
            type: 'reject',
            params: {
              reason: 'over age',
            },
          },
        },
        selectAllergies: {
          on: {
            back: {
              target: 'haveAllergies',
            },
            next: {
              target: 'medications',
              actions: 'storeQuestionnaire',
            },
          },
          meta: {
            step: 15,
          },
        },
        rejectedUnderAge: {
          on: {
            back: {
              target: 'age',
              actions: 'clearRejected',
            },
          },
          meta: {
            step: 5,
          },
          entry: {
            type: 'reject',
            params: {
              reason: 'under age',
            },
          },
        },
        medicalConditions: {
          on: {
            back: {
              target: 'medications',
            },
            next: {
              target: 'additionalInformation',
              actions: 'storeQuestionnaire',
            },
          },
          meta: {
            step: 17,
          },
        },
        recommendSeeDoctor: {
          on: {
            back: {
              target: 'doctorVisits',
            },
            next: {
              target: 'qualifyingConditions',
            },
          },
          meta: {
            step: 9,
          },
        },
        rejectedIsPregnant: {
          on: {
            back: {
              target: 'isPregnant',
              actions: 'clearRejected',
            },
          },
          meta: {
            step: 7,
          },
          entry: {
            type: 'reject',
            params: {
              reason: 'is pregnant',
            },
          },
        },
        qualifyingConditions: {
          on: {
            back: {
              target: 'doctorVisits',
            },
            next: {
              target: 'height',
              actions: 'storeQuestionnaire',
            },
          },
          meta: {
            step: 10,
          },
        },
        additionalInformation: {
          on: {
            back: {
              target: 'medicalConditions',
            },
            next: {
              target: 'finished',
              actions: 'storeQuestionnaire',
            },
          },
          meta: {
            step: 18,
          },
        },
        rejectedPriorConditions: {
          on: {
            back: {
              target: 'haveDiabetes',
              actions: 'clearRejected',
            },
          },
          meta: {
            step: 7,
          },
          entry: {
            type: 'reject',
            params: {
              reason: 'has prior conditions',
            },
          },
        },
      },
    },
    selectTreatmentType: {
      on: {
        next: {
          target: 'selectTreatment',
          actions: 'store',
        },
        back: {
          target: 'questionnaire.additionalInformation',
        },
      },
      meta: { step: 1, name: 'Virtual Doctor Visit' },
    },
    selectTreatment: {
      on: {
        next: {
          target: 'info',
          actions: 'store',
        },
        back: {
          target: 'selectTreatmentType',
        },
      },
      meta: { step: 2, name: 'Virtual Doctor Visit' },
    },
    info: {
      on: {
        next: {
          target: 'uploadIDPhoto',
        },
        back: {
          target: 'selectTreatment',
        },
      },
      meta: { step: 3, name: 'Virtual Doctor Visit' },
    },
    uploadIDPhoto: {
      on: {
        next: {
          target: 'uploadFacePhoto',
          actions: 'store',
        },
        back: {
          target: 'info',
        },
      },
      meta: { step: 4, name: 'Virtual Doctor Visit' },
    },
    uploadFacePhoto: {
      on: {
        next: {
          target: 'visitCompletion',
          actions: 'store',
        },
        back: {
          target: 'uploadIDPhoto',
        },
      },
      meta: { step: 5, name: 'Virtual Doctor Visit' },
    },
    visitCompletion: {
      on: {
        next: {
          target: 'summary',
        },
        back: {
          target: 'uploadFacePhoto',
        },
      },
      meta: { step: 6, name: 'Checkout' },
    },
    summary: {
      on: {
        next: {
          target: 'shipping',
        },
        back: {
          target: 'visitCompletion',
        },
      },
      meta: { step: 7, name: 'Checkout' },
    },
    shipping: {
      on: {
        next: {
          target: 'payment',
          actions: 'store',
        },
        back: {
          target: 'summary',
        },
      },
      meta: { step: 8, name: 'Checkout' },
    },
    payment: {
      on: {
        update: {
          actions: 'storeClientSecret',
        },
        complete: {
          target: 'onboarded',
        },
        back: {
          target: 'shipping',
        },
      },
      meta: { step: 9, name: 'Checkout' },
    },
    onboarded: {
      type: 'final',
      meta: { step: 10 },
    },
  },
});
