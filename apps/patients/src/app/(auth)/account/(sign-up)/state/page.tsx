'use client';

import React, { useEffect } from 'react';
import Image from 'next/image';
import Link from 'next/link';
import arrow from '@/assets/svg/arrow-circle.svg';
import OnboardingTitle from '@/components/onboarding/OnboardingTitle';
import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormLoader,
  FormMessage,
} from '@/components/ui/form';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { useAnalytics } from '@/context/AnalyticsContext';
import { useOnboardingNavigation } from '@/hooks/useOnboardingNavigation';
import { useOnboardingService } from '@/hooks/useOnboardingService';
import { useStates } from '@/hooks/useStates';
import { onboardingDataAtom, onboardingStepsAtom } from '@/store/store';
import { zodResolver } from '@hookform/resolvers/zod';
import { useAtomValue, useSetAtom } from 'jotai';
import { useForm } from 'react-hook-form';
import { z } from 'zod';

const schema = z.object({
  state: z.string().min(1, { message: 'State is required' }),
  agree: z
    .boolean()
    .refine((data) => data, { message: 'Please agree to the terms' }),
});

const StateSelection = () => {
  const setSteps = useSetAtom(onboardingStepsAtom);
  const onboardingData = useAtomValue(onboardingDataAtom);
  const { preSignupSetState } = useOnboardingService();
  const { nextOnboardingStep } = useOnboardingNavigation();
  const analytics = useAnalytics();

  const form = useForm<z.infer<typeof schema>>({
    resolver: zodResolver(schema),
    defaultValues: {
      state: onboardingData?.context?.state || '',
      agree: false,
    },
  });

  const { data: states, isPending: statesPending } = useStates();

  useEffect(() => {
    setSteps({
      stepName: 'Account Creation',
      percentage: 0,
      back: undefined,
    });
    analyticsOnboardingStarted();
  }, []);

  const analyticsOnboardingStarted = () => {
    if (!analytics) return;
    void analytics.track('Onboarding Started', { version: 'v1' });
  };

  const onSubmit = async ({ state }: z.infer<typeof schema>) => {
    try {
      const response = await preSignupSetState.mutateAsync({ state });

      void analytics?.track('State Selected', { state, version: 'v1' });

      nextOnboardingStep({
        state: response.currentState,
        context: response.context || {},
        events: [],
        stepName: response.stepName || 'Account Creation',
        percentage: response.percentage,
      });
    } catch (error) {
      console.error('Failed to select state:', error);
    }
  };

  useEffect(() => {
    // Clean up any Google Analytics parameters
    if (window.location.search.includes('_gl=')) {
      const cleanUrl = window.location.href.replace(/(\?|&)_gl=[^&]*/, '');
      window.history.replaceState({}, document.title, cleanUrl);
    }
  }, []);

  return (
    <OnboardingTitle title="What state do you live in?">
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="">
          <FormLoader isLoading={form.formState.isSubmitting}>
            <div className="flex w-full flex-col items-start justify-start gap-10">
              {!statesPending && (
                <FormField
                  control={form.control}
                  name={'state'}
                  render={({ field }) => (
                    <FormItem className="grid w-full gap-1 py-2">
                      <FormControl>
                        <Select
                          onValueChange={field.onChange}
                          defaultValue={field.value}
                        >
                          <SelectTrigger className="w-full bg-denim-light py-6">
                            <SelectValue placeholder="Select your State" />
                          </SelectTrigger>
                          <SelectContent>
                            {states?.map((state) => (
                              <SelectItem key={state.code} value={state.code}>
                                {state.name}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              )}

              <FormField
                control={form.control}
                name="agree"
                render={({ field }) => (
                  <FormItem>
                    <FormControl>
                      <div className="inline-flex w-full items-center justify-start gap-5">
                        <Checkbox
                          id="agree"
                          checked={field.value}
                          onCheckedChange={field.onChange}
                          className="data-[state=checked]:bg-white"
                        />
                        <FormLabel
                          htmlFor="agree"
                          className="text-sm text-white"
                        >
                          I have read and agree to these{' '}
                          <Link
                            className={'underline'}
                            href={
                              'https://www.startwillow.com/terms-and-conditions'
                            }
                            target="_blank"
                          >
                            Terms & Conditions
                          </Link>
                          ,{' '}
                          <Link
                            className={'underline'}
                            href={'https://www.startwillow.com/privacy-policy'}
                            target="_blank"
                          >
                            Privacy Policy
                          </Link>
                          , and{' '}
                          <Link
                            className={'underline'}
                            href={
                              'https://www.startwillow.com/informed-consent'
                            }
                            target="_blank"
                          >
                            Telehealth Consent
                          </Link>
                        </FormLabel>
                      </div>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <Button
                variant="electric"
                className="flex w-full max-w-none justify-between"
                type="submit"
                size="lg"
                disabled={preSignupSetState.isPending}
              >
                CONTINUE
                <Image
                  alt="arrow"
                  src={arrow}
                  style={{ objectFit: 'contain' }}
                />
              </Button>

              <div className="flex w-full items-center justify-center gap-1">
                <span className="text-xl font-medium leading-tight text-white">
                  Already have an account?
                </span>

                <Link
                  className="text-xl font-medium leading-tight text-electric"
                  href="/"
                >
                  Sign in
                </Link>
              </div>
            </div>
          </FormLoader>
        </form>
      </Form>
    </OnboardingTitle>
  );
};

export default StateSelection;
