'use client';

import React, { useEffect } from 'react';
import Image from 'next/image';
import Link from 'next/link';
import arrow from '@/assets/svg/arrow-circle.svg';
import OnboardingTitle from '@/components/onboarding/OnboardingTitle';
import { Button } from '@/components/ui/button';
import { useAnalytics } from '@/context/AnalyticsContext';

const UnsupportedStateThankYou = () => {
  const analytics = useAnalytics();

  useEffect(() => {
    if (analytics) {
      void analytics.track('Unsupported State Waitlist Joined', {
        version: 'v1',
      });
    }
  }, [analytics]);

  return (
    <OnboardingTitle title="Thank you for visiting <PERSON>. We will reach out to you as soon as we are in your state!">
      <Link href="https://www.startwillow.com/">
        <Button
          size="lg"
          variant="electric"
          className="flex w-full max-w-none justify-between uppercase"
        >
          <span>Return to homepage</span>
          <Image alt="arrow" src={arrow} style={{ objectFit: 'contain' }} />
        </Button>
      </Link>
    </OnboardingTitle>
  );
};

export default UnsupportedStateThankYou;
