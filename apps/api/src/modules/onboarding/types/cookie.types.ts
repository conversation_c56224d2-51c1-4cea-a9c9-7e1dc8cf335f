import type { Snapshot<PERSON>rom } from 'xstate';

import type { onboardingV1Machine } from '../states/versions/onboarding-v1.state';

/**
 * Supported onboarding versions
 */
export type OnboardingVersion = 'v1' | 'legacy'; // Add 'v2', 'v3' etc. as needed

/**
 * Type mapping for version to state machine snapshot
 */
export type OnboardingStateSnapshot<T extends OnboardingVersion> =
  T extends 'v1'
    ? SnapshotFrom<typeof onboardingV1Machine>
    : T extends 'legacy'
      ? never
      : // T extends 'v2' ? SnapshotFrom<typeof onboardingV2Machine> :
        never;

/**
 * Base cookie structure without version-specific snapshot
 */
export interface BaseOnboardingCookie {
  startedAt: string;
  currentState?: string;
  authTokens?: OnboardingAuthTokens;
}

/**
 * Version-specific cookie with proper snapshot typing
 */
export interface VersionedOnboardingCookie<T extends OnboardingVersion>
  extends BaseOnboardingCookie {
  version: T;
  stateSnapshot?: OnboardingStateSnapshot<T>;
}

/**
 * Union type for all possible cookie versions
 * This will automatically expand as new versions are added
 */
export type OnboardingCookie = {
  [K in OnboardingVersion]: VersionedOnboardingCookie<K>;
}[OnboardingVersion];

/**
 * Auth tokens structure for authenticated onboarding cookie
 */
export interface OnboardingAuthTokens {
  accessToken: string;
  refreshToken: string;
  role: string;
  patientId: string;
}

/**
 * Type guard to check if a cookie has auth tokens
 */
export function isAuthenticatedCookie(
  cookie: any,
): cookie is BaseOnboardingCookie & { authTokens: OnboardingAuthTokens } {
  return (
    cookie &&
    typeof cookie === 'object' &&
    'authTokens' in cookie &&
    cookie.authTokens &&
    typeof cookie.authTokens === 'object' &&
    'accessToken' in cookie.authTokens &&
    'refreshToken' in cookie.authTokens &&
    'role' in cookie.authTokens &&
    'patientId' in cookie.authTokens
  );
}
