import { PreventImpersonatedEditGuard } from '@modules/auth/guards/prevent-impersonated-edit.guard';
import { RolesGuard } from '@modules/auth/guards/roles.guard';
import { Roles } from '@modules/auth/roles.decorator';
import { roles } from '@modules/auth/types/roles';
import { DesiredTreatmentTypeDTO } from '@modules/onboarding/dto/desired-treatment-type.dto';
import { DesiredTreatmentDTO } from '@modules/onboarding/dto/desired-treatment.dto';
import { QuestionnaireDto } from '@modules/onboarding/dto/questionnaire.dto';
import { ShippingInfoDto } from '@modules/onboarding/dto/shipping-info.dto';
import { UploadPhotoDto } from '@modules/onboarding/dto/upload-photo.dto';
import { PatientOnboardingService } from '@modules/onboarding/services/patient-onboarding.service';
import { OnboardingCheckOnboardedUseCase } from '@modules/onboarding/use-cases/onboarding-check-onboarded.use-case.service';
import { OnboardingDesiredTreatmentTypeUseCase } from '@modules/onboarding/use-cases/onboarding-desired-treatment-type.use-case';
import { OnboardingDesiredTreatmentUseCase } from '@modules/onboarding/use-cases/onboarding-desired-treatment.use-case';
import { OnboardingGetQuestionnaireStatusUseCase } from '@modules/onboarding/use-cases/onboarding-get-questionnaire-status.use-case';
import { OnboardingGetRecommendedTreatmentUseCase } from '@modules/onboarding/use-cases/onboarding-get-recommended-treatment.use-case';
import { OnboardingGetTreatmentTypeUseCase } from '@modules/onboarding/use-cases/onboarding-get-treatment-type-use-case.service';
import { OnboardingNextUseCase } from '@modules/onboarding/use-cases/onboarding-next.use-case';
import { OnboardingQuestionnaireUseCase } from '@modules/onboarding/use-cases/onboarding-questionnaire.use-case';
import { OnboardingSetupPaymentIntentUseCase } from '@modules/onboarding/use-cases/onboarding-setup-payment-intent.use-case';
import { OnboardingShippingInfoUseCase } from '@modules/onboarding/use-cases/onboarding-shipping-info.use-case';
import { OnboardingStorePhotoUseCase } from '@modules/onboarding/use-cases/onboarding-store-photo-use.case';
import { OnboardingUpdateBillingAddressUseCase } from '@modules/onboarding/use-cases/onboarding-update-billing-address.use-case';
import { PatientGetPreSignedUrlUseCase } from '@modules/patient/use-cases/patient-get-pre-signed-url.use-case';
import { AddressMismatchError } from '@modules/shared/errors/address-mismatch.error';
import { PoBoxError } from '@modules/shared/errors/po-box.error';
import {
  BadRequestException,
  Body,
  ConflictException,
  Controller,
  Get,
  Param,
  Post,
  Req,
  UseGuards,
} from '@nestjs/common';
import { AuthGuard } from '@nestjs/passport';
import { Request } from 'express';

import { OnboardingAttachedDiscountUseCase } from './use-cases/onboarding-attached-discount.use-case';
import { OnboardingBackUseCase } from './use-cases/onboarding-back.use-case';

@Controller('onboarding')
@UseGuards(AuthGuard('jwt'), RolesGuard, PreventImpersonatedEditGuard)
@Roles(roles.Patient)
export class OnboardingController {
  constructor(
    private readonly patientOnboardingService: PatientOnboardingService,
    private readonly onboardingGetQuestionnaireStatusUseCase: OnboardingGetQuestionnaireStatusUseCase,
    private readonly onboardingBackUseCase: OnboardingBackUseCase,
    private readonly onboardingNextUseCase: OnboardingNextUseCase,
    private readonly onboardingQuestionnaireUseCase: OnboardingQuestionnaireUseCase,
    private readonly onboardingGetTreatmentTypeUseCase: OnboardingGetTreatmentTypeUseCase,
    private readonly onboardingGetRecommendedTreatmentUseCase: OnboardingGetRecommendedTreatmentUseCase,
    private readonly onboardingDesiredTreatmentUseCase: OnboardingDesiredTreatmentUseCase,
    private readonly onboardingDesiredTreatmentTypeUseCase: OnboardingDesiredTreatmentTypeUseCase,
    private readonly patientGetPreSignedUrlUseCase: PatientGetPreSignedUrlUseCase,
    private readonly onboardingStorePhotoUseCase: OnboardingStorePhotoUseCase,
    private readonly onboardingShippingInfoUseCase: OnboardingShippingInfoUseCase,
    private readonly onboardingSetupPaymentIntentUseCase: OnboardingSetupPaymentIntentUseCase,
    private readonly onboardingUpdateBillingAddressUseCase: OnboardingUpdateBillingAddressUseCase,
    private readonly onboardingCheckOnboardedUseCase: OnboardingCheckOnboardedUseCase,
    private readonly onboardingAttachedDiscountUseCase: OnboardingAttachedDiscountUseCase,
  ) {}

  @Get('status')
  async status(@Req() request: Request) {
    try {
      const user = await this.patientOnboardingService.getPatientData(request);

      return await this.onboardingGetQuestionnaireStatusUseCase.execute(user);
    } catch (e) {
      throw new BadRequestException(e.message);
    }
  }

  @Get('/discount/attached')
  @UseGuards(AuthGuard('jwt'), RolesGuard)
  @Roles(roles.Patient)
  async getAttachedDiscountToPatient(@Req() request: Request) {
    const userId = request.user['userId'] as string;
    return this.onboardingAttachedDiscountUseCase.execute({ userId });
  }

  @Post('next')
  async next(@Req() request: Request) {
    try {
      const profile =
        await this.patientOnboardingService.getPatientData(request);

      return await this.onboardingNextUseCase.execute(profile);
    } catch (e) {
      throw new BadRequestException(e.message);
    }
  }

  @Post('back')
  async back(@Req() request: Request) {
    try {
      const profile =
        await this.patientOnboardingService.getPatientData(request);

      return await this.onboardingBackUseCase.execute(profile);
    } catch (e) {
      throw new BadRequestException(e.message);
    }
  }

  @Post('questionnaire')
  async questionnaire(
    @Req() request: Request,
    @Body() requestBody: QuestionnaireDto,
  ) {
    try {
      const profile =
        await this.patientOnboardingService.getPatientData(request);

      // store new questionnaire answers
      return await this.onboardingQuestionnaireUseCase.execute(
        profile,
        requestBody,
      );
    } catch (e) {
      throw new BadRequestException(e.message);
    }
  }

  @Get('treatment-types')
  async treatmentTypes(@Req() request: Request) {
    try {
      const profile =
        await this.patientOnboardingService.getPatientData(request);

      return await this.onboardingGetTreatmentTypeUseCase.execute(profile);
    } catch (e) {
      throw new BadRequestException(e.message);
    }
  }

  @Post('desired-treatment-type')
  async desiredTreatmentType(
    @Req() request: Request,
    @Body() requestBody: DesiredTreatmentTypeDTO,
  ) {
    try {
      const profile =
        await this.patientOnboardingService.getPatientData(request);

      return await this.onboardingDesiredTreatmentTypeUseCase.execute(
        profile,
        requestBody.categoryId,
      );
    } catch (e) {
      throw new BadRequestException(e.message);
    }
  }

  @Get('recommended-treatment/:type')
  async recommendedTreatment(
    @Req() request: Request,
    @Param('type') type: string,
  ) {
    try {
      const profile =
        await this.patientOnboardingService.getPatientData(request);

      return await this.onboardingGetRecommendedTreatmentUseCase.execute(
        profile,
        type,
      );
    } catch (e) {
      throw new BadRequestException(e.message);
    }
  }

  @Post('desired-treatment')
  async desiredTreatment(
    @Req() request: Request,
    @Body() requestBody: DesiredTreatmentDTO,
  ) {
    try {
      const profile =
        await this.patientOnboardingService.getPatientData(request);

      return await this.onboardingDesiredTreatmentUseCase.execute(
        profile,
        requestBody,
      );
    } catch (e) {
      throw new BadRequestException(e.message);
    }
  }

  @Get('get-upload-url/:type')
  async getUploadUrl(@Req() request: Request, @Param('type') type: string) {
    try {
      const profile =
        await this.patientOnboardingService.getPatientData(request);

      // generate upload url
      return await this.patientGetPreSignedUrlUseCase.execute(profile, type);
    } catch (e) {
      throw new BadRequestException(e.message);
    }
  }

  @Post('photo')
  async photo(@Req() request: Request, @Body() requestBody: UploadPhotoDto) {
    try {
      const { type, skip } = requestBody;
      const profile =
        await this.patientOnboardingService.getPatientData(request);

      return await this.onboardingStorePhotoUseCase.execute(
        profile,
        type,
        skip,
      );
    } catch (e) {
      throw new BadRequestException(e.message);
    }
  }

  @Post('shipping-info')
  async shippingInfo(
    @Req() request: Request,
    @Body() requestBody: ShippingInfoDto,
  ) {
    try {
      const profile =
        await this.patientOnboardingService.getPatientData(request);
      return await this.onboardingShippingInfoUseCase.execute(
        profile,
        requestBody,
      );
    } catch (e) {
      if (e instanceof AddressMismatchError) {
        throw new ConflictException({
          message: 'Address validation failed',
          error: 'Conflict',
          statusCode: 409,
          proposedAddress: e.address,
        });
      } else if (e instanceof PoBoxError) {
        throw new BadRequestException({
          message: e.message,
        });
      }
      throw new BadRequestException(e.message);
    }
  }

  @Post('setup-payment-intent')
  async setupPaymentIntent(@Req() request: Request) {
    try {
      const profile =
        await this.patientOnboardingService.getPatientData(request);
      return await this.onboardingSetupPaymentIntentUseCase.execute(profile);
    } catch (e) {
      throw new BadRequestException(e.message);
    }
  }

  @Post('update-billing-address')
  async updateBillingAddress(
    @Req() request: Request,
    @Body() requestBody: ShippingInfoDto,
  ) {
    try {
      const profile =
        await this.patientOnboardingService.getPatientData(request);
      return await this.onboardingUpdateBillingAddressUseCase.execute(
        profile,
        requestBody,
      );
    } catch (e) {
      if (e instanceof AddressMismatchError) {
        throw new ConflictException({
          message: 'Address validation failed',
          error: 'Conflict',
          statusCode: 409,
          proposedAddress: e.address,
        });
      } else if (e instanceof PoBoxError) {
        throw new BadRequestException({
          message: e.message,
          error: 'Bad Request',
        });
      }
      throw new BadRequestException(e.message);
    }
  }

  @Get('onboarded')
  async onboarded(@Req() request: Request) {
    try {
      const profile =
        await this.patientOnboardingService.getPatientData(request);
      return await this.onboardingCheckOnboardedUseCase.execute(profile);
    } catch (e) {
      throw new BadRequestException(e.message);
    }
  }
}
