import React from 'react';
import { onboardingStepsAtom } from '@/store/store';
import { useAtomValue } from 'jotai/index';

import BackButtonOnboarding from './onboarding/BackButtonOnboarding';

const StepCounter = () => {
  const { stepName, percentage } = useAtomValue(onboardingStepsAtom);

  if (!stepName) return null;

  return (
    <div className="inline-flex w-full flex-col items-start justify-center gap-5">
      <div className="inline-flex items-center justify-start gap-2.5 self-stretch">
        <div className="inline-flex shrink grow basis-0 flex-col items-start justify-center gap-5">
          <div className="inline-flex items-center justify-between self-stretch">
            <div className="hidden items-center justify-start md:inline-flex">
              <BackButtonOnboarding />
            </div>
            <div className="flex items-center justify-start gap-2.5">
              <span className="text-xl text-white md:text-glass">
                {stepName}
              </span>
            </div>
          </div>
          <div className="hidden h-0.5 flex-col items-start justify-center gap-2.5 self-stretch rounded-sm bg-slate-500 md:flex">
            <div
              className="relative h-0.5 w-40 rounded-sm bg-white"
              style={{ width: `${percentage}%` }}
            />
          </div>
        </div>
      </div>
    </div>
  );
};

export default StepCounter;
