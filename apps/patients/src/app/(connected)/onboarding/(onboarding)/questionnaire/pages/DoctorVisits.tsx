import type { OnboardingProps } from '@/app/(connected)/onboarding/(onboarding)/questionnaire/[slug]/page';
import RadioGroupQuestion from '@/components/onboarding/RadioGroupQuestion';

const DoctorVisits = (props: OnboardingProps) => {
  const config = {
    fieldName: 'doctorVisits',
    title: 'Have you seen a doctor in the last two years?',
    subtitle: `Your medical history helps us determine if you're eligible for <PERSON>'s program.`,
    options: [
      { value: 'yes', label: 'Yes' },
      { value: 'no', label: 'No' },
    ],
    schemaValues: ['yes', 'no'] as [string, string],
    errorMessage: 'Please select an option',
    getContextValue: (context: any) => context.questionnaire.doctorVisits,
  };

  return <RadioGroupQuestion {...props} config={config} />;
};

export default DoctorVisits;
