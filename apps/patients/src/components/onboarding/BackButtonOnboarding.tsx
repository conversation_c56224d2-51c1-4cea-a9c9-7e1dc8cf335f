import { useEffect, useState } from 'react';
import { usePathname } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { useOnboardingBack } from '@/hooks/onboarding';
import { useOnboardingNavigation } from '@/hooks/useOnboardingNavigation';
import { useOnboardingService } from '@/hooks/useOnboardingService';
import { onboardingStepsAtom } from '@/store/store';
import { useAtomValue } from 'jotai/index';
import { MoveLeft } from 'lucide-react';

const BackButtonOnboarding = () => {
  const [showBack, setShowBack] = useState(false);
  const { stepName } = useAtomValue(onboardingStepsAtom);
  const { onboarding, nextOnboardingStep } = useOnboardingNavigation();
  const backMutation = useOnboardingBack();
  const { preSignupBack } = useOnboardingService();

  const path = usePathname();

  const onBack = async () => {
    try {
      // Check if we're in pre-signup flow (account pages)
      const isPreSignup = path.startsWith('/account');

      if (isPreSignup) {
        // Pre-signup flow - use preSignupBack
        const response = await preSignupBack.mutateAsync();
        nextOnboardingStep({
          state: response.currentState,
          context: response.context || {},
          events: [],
          stepName: response.stepName || 'Account Creation',
          percentage: response.percentage || 0,
        });
      } else {
        // Authenticated flow - use authenticated back
        const response = await backMutation.mutateAsync();
        nextOnboardingStep(response.data);
      }
    } catch (error) {
      console.error('back error', error);
    }
  };

  useEffect(() => {
    const onboardingBack = onboarding?.events?.includes('back');
    const accountBack =
      path.startsWith('/account') &&
      path !== '/account/state' &&
      path !== '/account/thank-you';
    const back = onboardingBack || accountBack;

    setShowBack(back);
  }, [onboarding, path]);

  if (!stepName) return null;

  return (
    showBack && (
      <Button
        variant={'link'}
        onClick={onBack}
        className="cursor-pointer text-xl font-medium leading-tight text-slate-500"
        disabled={backMutation.isPending || preSignupBack.isPending}
      >
        <div className="hidden gap-2.5 md:flex">
          <MoveLeft />
          Back
        </div>

        <div className="rounded-3xl border border-glass p-2 md:hidden">
          <MoveLeft className="text-white" />
        </div>
      </Button>
    )
  );
};

export default BackButtonOnboarding;
