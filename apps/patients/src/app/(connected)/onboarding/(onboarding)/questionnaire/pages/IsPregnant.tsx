import type { OnboardingProps } from '@/app/(connected)/onboarding/(onboarding)/questionnaire/[slug]/page';
import RadioGroupQuestion from '@/components/onboarding/RadioGroupQuestion';

const IsPregnant = (props: OnboardingProps) => {
  const config = {
    fieldName: 'isPregnant',
    title: 'Are you pregnant, lactating, or trying to get pregnant?',
    subtitle: `Your pregnancy status and goals help us determine if you're eligible for <PERSON>'s program.`,
    options: [
      { value: 'yes', label: 'Yes' },
      { value: 'no', label: 'No' },
    ],
    schemaValues: ['yes', 'no'] as [string, string],
    errorMessage: 'Please answer the question',
    getContextValue: (context: any) => context.questionnaire.isPregnant,
  };

  return <RadioGroupQuestion {...props} config={config} />;
};

export default IsPregnant;
