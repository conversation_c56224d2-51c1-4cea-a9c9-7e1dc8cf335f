import { AuditService } from '@/modules/audit-log/audit-log.service';
import {
  PatientOnboardingProfile,
  PatientPersistence,
} from '@adapters/persistence/database/patient.persistence';
import { ProductPersistence } from '@adapters/persistence/database/product.persistence';
import { DesiredTreatmentDTO } from '@modules/onboarding/dto/desired-treatment.dto';
import {
  OnboardingActor,
  OnboardingSnapshot,
  OnboardingStateService,
} from '@modules/onboarding/services/onboarding-state.service';
import { OnboardingVersion } from '@modules/onboarding/states/versions';
import { Injectable } from '@nestjs/common';

import { OnboardingEventEmitterService } from './onboarding-event-emitter.service';

export type DesiredTreatment = {
  id: string;
  name: string;
  description: string;
  image: string;
  price: number;
  type: string;
  metadata: string;
  vials: number;
  dosageLabel: string;
  form: string;
  label: string;
};

@Injectable()
export class OnboardingDesiredTreatmentUseCase {
  constructor(
    private readonly onboardingService: OnboardingStateService,
    private readonly patientPersistence: PatientPersistence,
    private readonly productPersistence: ProductPersistence,
    private readonly onboardingEventEmitter: OnboardingEventEmitterService,
    private readonly auditService: AuditService,
  ) {}

  async execute(
    profile: PatientOnboardingProfile,
    desiredTreatment: DesiredTreatmentDTO,
  ) {
    //check products exist
    const products =
      await this.productPersistence.getActiveWithDefaultPriceByIds(
        desiredTreatment.products,
      );
    if (products.length !== desiredTreatment.products.length) {
      throw new Error('Invalid Treatment selected');
    }

    const onboarding = (await this.onboardingService.getCurrentOnboardingActor(
      (profile.onboardingVersion || 'legacy') as OnboardingVersion,
      profile.onboardingState as unknown as OnboardingSnapshot,
    )) as OnboardingActor;

    //validate it's on selectTreatment state
    const state = onboarding.getSnapshot();
    if (state.value !== 'selectTreatment') {
      throw new Error('Invalid onboarding state');
    }
    // perform transition and get new state
    const payload = products.map((p) => {
      return {
        id: p.id,
        name: p.name,
        description: p.description,
        image: p.image,
        price: p.defaultPrice.unit_amount / 100,
        type: p.isCore ? 'core' : 'additional',
        metadata: p.metadata,
        vials: p.isCore ? desiredTreatment.vials : 1,
        dosageLabel: p.defaultPrice.dosageLabel,
        form: p.form,
        label: p.label,
      } as DesiredTreatment;
    });
    const result = this.onboardingService.performTransition(
      onboarding,
      'next',
      { products: payload },
    );

    const snapshot = result.getSnapshot();

    if (!desiredTreatment.products.length) {
      delete snapshot.context.pharmacyId;
    }

    void this.auditService.append({
      patientId: profile.id,
      action: 'ONBOARDING_TREATMENT_SELECTED',
      actorType: 'PATIENT',
      actorId: profile.id,
      resourceType: 'PATIENT',
      resourceId: profile.id,
      details: {
        selectedTreatment: payload,
      },
    });

    // persist new state
    await this.patientPersistence.updateOnboarding(profile.id, snapshot);

    void this.onboardingEventEmitter.execute(profile, snapshot);

    return this.onboardingService.getCurrentOnboardingState(
      (profile.onboardingVersion || 'legacy') as OnboardingVersion,
      snapshot,
    );
  }
}
