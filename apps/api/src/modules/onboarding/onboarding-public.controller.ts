import { Body, Controller, Get, Param, Post, UseGuards } from '@nestjs/common';

import { PreSignupCreateAccountDto } from './dto/pre-signup/create-account.dto';
import { PreSignupNameDto } from './dto/pre-signup/name.dto';
import { PreSignupStateCheckDto } from './dto/pre-signup/state-check.dto';
import { PreSignupWaitingListDto } from './dto/pre-signup/waiting-list.dto';
import { OnboardingPublicOnlyGuard } from './guards/onboarding-public-only.guard';
import { OnboardingInitializeUseCase } from './use-cases/onboarding-initialize.use-case';
import { OnboardingPreSignupBackUseCase } from './use-cases/onboarding-pre-signup-back.use-case';
import { OnboardingPreSignupCreateAccountUseCase } from './use-cases/onboarding-pre-signup-create-account.use-case';
import { OnboardingPreSignupNameUseCase } from './use-cases/onboarding-pre-signup-name.use-case';
import { OnboardingPreSignupNextUseCase } from './use-cases/onboarding-pre-signup-next.use-case';
import { OnboardingPreSignupStateUseCase } from './use-cases/onboarding-pre-signup-state.use-case';
import { OnboardingPreSignupWaitingListUseCase } from './use-cases/onboarding-pre-signup-waiting-list.use-case';
import { OnboardingValidateDiscountUseCase } from './use-cases/onboarding-validate-discount.use-case';

@Controller('onboarding')
@UseGuards(OnboardingPublicOnlyGuard)
export class OnboardingPublicController {
  constructor(
    private readonly onboardingValidateDiscountUseCase: OnboardingValidateDiscountUseCase,
    private readonly onboardingInitializeUseCase: OnboardingInitializeUseCase,
    private readonly onboardingPreSignupBackUseCase: OnboardingPreSignupBackUseCase,
    private readonly onboardingPreSignupNextUseCase: OnboardingPreSignupNextUseCase,
    private readonly onboardingPreSignupStateUseCase: OnboardingPreSignupStateUseCase,
    private readonly onboardingPreSignupNameUseCase: OnboardingPreSignupNameUseCase,
    private readonly onboardingPreSignupWaitingListUseCase: OnboardingPreSignupWaitingListUseCase,
    private readonly onboardingPreSignupCreateAccountUseCase: OnboardingPreSignupCreateAccountUseCase,
  ) {}

  @Post('initialize')
  async initialize() {
    return this.onboardingInitializeUseCase.execute();
  }

  @Post('pre-signup/back')
  async preSignupBack() {
    return this.onboardingPreSignupBackUseCase.execute();
  }

  @Post('pre-signup/next')
  async preSignupNext() {
    return this.onboardingPreSignupNextUseCase.execute();
  }

  @Post('pre-signup/set-state')
  async preSignupCheckState(@Body() body: PreSignupStateCheckDto) {
    return this.onboardingPreSignupStateUseCase.execute(body);
  }

  @Post('pre-signup/waiting-list')
  async preSignupWaitingList(@Body() body: PreSignupWaitingListDto) {
    return this.onboardingPreSignupWaitingListUseCase.execute(body);
  }

  @Post('pre-signup/name')
  async preSignupName(@Body() body: PreSignupNameDto) {
    return this.onboardingPreSignupNameUseCase.execute(body);
  }

  @Post('pre-signup/create-account')
  async preSignupCreateAccount(@Body() body: PreSignupCreateAccountDto) {
    return this.onboardingPreSignupCreateAccountUseCase.execute(body);
  }

  @Get('discount/:type/:code')
  async validateDiscount(
    @Param('type') type: 'referral' | 'coupon',
    @Param('code') code: string,
  ) {
    return this.onboardingValidateDiscountUseCase.execute({
      discount: { type, code },
    });
  }
}
