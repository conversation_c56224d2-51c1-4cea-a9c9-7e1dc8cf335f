export interface PreSignupFlow {
  initialState: string;
  states: any; // Using any to avoid complex XState type issues
  meta?: {
    version: string;
    total: number;
  };
}

export interface OnboardingInitializeResponse {
  version: 'v1' | 'legacy';
  initialized: boolean;
  preSignupFlow?: PreSignupFlow | null;
  currentState?: string;
  canTransition?: boolean;
  isComplete?: boolean;
  context?: any;
  events?: string[];
  stepName?: string;
  percentage?: number;
}
