import type { OnboardingProps } from '@/app/(connected)/onboarding/(onboarding)/questionnaire/[slug]/page';
import RadioGroupQuestion from '@/components/onboarding/RadioGroupQuestion';

const HaveDiabetes = (props: OnboardingProps) => {
  const config = {
    fieldName: 'haveDiabetes',
    title: 'Do you have diabetes?',
    subtitle: `Your medical history helps us determine if you're eligible for <PERSON>'s program.`,
    options: [
      { value: 'yes', label: 'Yes' },
      { value: 'no', label: 'No' },
    ],
    schemaValues: ['yes', 'no'] as [string, string],
    errorMessage: 'Please select an option',
    getContextValue: (context: any) => context.questionnaire.haveDiabetes,
  };

  return <RadioGroupQuestion {...props} config={config} />;
};

export default HaveDiabetes;
