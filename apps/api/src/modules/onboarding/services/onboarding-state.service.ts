import { QuestionnairePersistence } from '@adapters/persistence/database/questionnaire.persistence';
import {
  onboardingMachine,
  onboardingMachineConfig,
} from '@modules/onboarding/states/onboarding.state';
import {
  ONBOARDING_VERSIONS,
  OnboardingVersion,
} from '@modules/onboarding/states/versions';
import { Injectable } from '@nestjs/common';
import { AnyMachineSnapshot, createActor } from 'xstate';

export type OnboardingActor = Awaited<
  ReturnType<OnboardingStateService['getCurrentOnboardingActor']>
>;
export type OnboardingSnapshot = ReturnType<OnboardingActor['getSnapshot']>;

export type OnboardingProfile = Awaited<
  ReturnType<OnboardingStateService['getCurrentOnboardingState']>
>;

@Injectable()
export class OnboardingStateService {
  private readonly SECTION_NAMES = {
    preSignup: 'Account Creation',
    questionnaire: 'Health Questionnaire',
    selectTreatmentType: 'Select Your Medication',
    selectTreatment: 'Select Your Medication',
    uploadIdPhoto: 'Upload Your Photos',
    uploadFacePhoto: 'Upload Your Photos',
    shippingInfo: 'Checkout',
    paymentInfo: 'Checkout',
    reviewOrder: 'Checkout',
    confirmPayment: 'Checkout',
    processingPayment: 'Checkout',
    onboarded: 'Completed',
  };
  constructor(
    private readonly questionnairePersistence: QuestionnairePersistence,
  ) {}

  /**
   * Builds the initial states for the onboarding machine and the current questionnaire,
   * and it's persisted to the database when a new patient is created
   */
  async buildInitialStates(onboardingVersion?: OnboardingVersion) {
    const onboarding =
      onboardingVersion === 'legacy' || !onboardingVersion
        ? await this.buildOnboardingMachine()
        : await this.buildVersionedOnboardingMachine(onboardingVersion);
    return onboarding.getSnapshot();
  }

  async getCurrentOnboardingActor(
    version: number,
    snapshot: AnyMachineSnapshot,
  ): Promise<any>;
  async getCurrentOnboardingActor(
    version: OnboardingVersion,
    snapshot: AnyMachineSnapshot,
  ): Promise<any>;
  async getCurrentOnboardingActor(
    version: number | OnboardingVersion,
    snapshot: AnyMachineSnapshot,
  ) {
    if (typeof version === 'string') {
      return await this.buildVersionedOnboardingMachine(version, snapshot);
    }
    return await this.buildOnboardingMachine(version, snapshot);
  }

  performTransition(actor: OnboardingActor, eventName: string, data?: any) {
    let snapshot = actor.getSnapshot();
    const event = data ? { type: eventName, value: data } : { type: eventName };
    if (!snapshot.can(event)) {
      throw new Error('Invalid transition');
    }
    actor.send(event);

    snapshot = actor.getSnapshot();
    if (snapshot.status === 'error') {
      throw new Error(
        `Error after transition: ${JSON.stringify(snapshot.value)}`,
      );
    }
    return actor;
  }

  private getStatePath(stateValue: any): string[] {
    if (typeof stateValue === 'string') {
      return [stateValue];
    }
    // Handle nested state objects like { questionnaire: 'age' }
    const keys = Object.keys(stateValue);
    if (keys.length === 1) {
      return [keys[0], ...this.getStatePath(stateValue[keys[0]])];
    }
    return [];
  }

  private getStateMetadata(snapshot: any): {
    stepName: string;
    percentage: number;
  } {
    const stateValue = snapshot.value;

    // Handle final state
    if (stateValue === 'onboarded') {
      return {
        stepName: this.SECTION_NAMES['onboarded'],
        percentage: 100,
      };
    }

    // Get state path (e.g., ['questionnaire', 'age'] or ['preSignup', 'stateSelection'])
    const statePath = this.getStatePath(stateValue);
    const rootState = statePath[0];

    // Navigate through the state configuration to find the current state's metadata
    let absoluteStep = 0;
    let name = '';
    let stateConfig = snapshot.machine.config.states;

    for (let i = 0; i < statePath.length; i++) {
      const stateName = statePath[i];
      if (stateConfig && stateConfig[stateName]) {
        // Get both step and name from the current state's meta if they exist
        if (stateConfig[stateName]?.meta) {
          absoluteStep = stateConfig[stateName].meta.step || absoluteStep;
          name = stateConfig[stateName].meta.name || name;
        }

        // Navigate to nested states for the next iteration
        if (i < statePath.length - 1 && stateConfig[stateName].states) {
          stateConfig = stateConfig[stateName].states;
        }
      }
    }

    // If no name was found in meta, fall back to SECTION_NAMES
    if (!name) {
      name = this.SECTION_NAMES[rootState] || '';
    }

    // Calculate timeline-based percentage
    let percentage = 0;

    if (rootState === 'preSignup') {
      // Timeline 1: Pre-signup (4 steps)
      const currentStep = Math.min(absoluteStep, 4);
      percentage = Math.round((currentStep / 4) * 100);
    } else {
      // Timeline 2: Main onboarding (questionnaire + post-questionnaire = 24 steps)
      let currentStep = 0;

      if (rootState === 'questionnaire') {
        // Questionnaire steps: absolute steps 5-19 map to timeline steps 1-15
        currentStep = absoluteStep - 4; // Step 5 becomes step 1, step 19 becomes step 15
      } else {
        // Post-questionnaire states
        const postQuestionnaireStepMap: Record<string, number> = {
          selectTreatmentType: 16,
          selectTreatment: 17,
          info: 18,
          uploadIDPhoto: 19,
          uploadFacePhoto: 20,
          visitCompletion: 21,
          summary: 22,
          shipping: 23,
          payment: 24,
        };

        currentStep = postQuestionnaireStepMap[rootState] || 16;
      }

      percentage = Math.round((currentStep / 24) * 100);
    }

    return {
      stepName: name,
      percentage,
    };
  }

  async getCurrentOnboardingState(version: number, snapshot: any): Promise<any>;
  async getCurrentOnboardingState(
    version: OnboardingVersion,
    snapshot: any,
  ): Promise<any>;
  async getCurrentOnboardingState(
    version: number | OnboardingVersion,
    snapshot: any,
  ) {
    const actor =
      typeof version === 'string'
        ? await this.getCurrentOnboardingActor(
            version as OnboardingVersion,
            snapshot,
          )
        : await this.getCurrentOnboardingActor(version as number, snapshot);

    const onboardingSnapshot = actor.getSnapshot();
    const { stepName, percentage } = this.getStateMetadata(onboardingSnapshot);

    // Get available events by checking what the actor can do
    const events = [];
    const commonEvents = ['back', 'submit', 'next', 'update', 'complete'];
    for (const eventType of commonEvents) {
      if (actor.getSnapshot().can({ type: eventType })) {
        events.push(eventType);
      }
    }

    return {
      state: onboardingSnapshot.value,
      context: onboardingSnapshot.context,
      events,
      stepName,
      percentage,
    };
  }

  private async buildVersionedOnboardingMachine(
    version: OnboardingVersion,
    snapshot?: any,
  ) {
    if (version === 'legacy') {
      // Fall back to database-based approach for legacy patients
      return this.buildOnboardingMachine(undefined, snapshot);
    }

    const versionedMachine = ONBOARDING_VERSIONS[version];

    if (!versionedMachine) {
      throw new Error(`Onboarding version ${version} not found`);
    }

    const actor = snapshot
      ? createActor(versionedMachine, { snapshot })
      : createActor(versionedMachine, {
          input: {
            version: version as 'v1',
            existingData: {},
          },
        });

    return actor.start();
  }

  private async buildOnboardingMachine(version?: number, snapshot?: any) {
    // @todo cache last questionnaire by version
    const questionnaire = !version
      ? await this.questionnairePersistence.getLastQuestionnaire('onboarding')
      : await this.questionnairePersistence.getQuestionnaireByVersion(
          'onboarding',
          version,
        );

    // get a copy of the base machine for onboarding
    const config = JSON.parse(JSON.stringify(onboardingMachineConfig));
    config.states.questionnaire = questionnaire.config;

    const machine = onboardingMachine.createMachine(config);

    const actor = snapshot
      ? createActor(machine, { snapshot })
      : createActor(machine);

    return actor.start();
  }
}
