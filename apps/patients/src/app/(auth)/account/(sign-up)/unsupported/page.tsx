'use client';

import React, { useEffect } from 'react';
import Image from 'next/image';
import arrow from '@/assets/svg/arrow-circle.svg';
import OnboardingTitle from '@/components/onboarding/OnboardingTitle';
import { Button } from '@/components/ui/button';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLoader,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { useOnboardingNavigation } from '@/hooks/useOnboardingNavigation';
import { useOnboardingService } from '@/hooks/useOnboardingService';
import { onboardingStepsAtom } from '@/store/store';
import { zodResolver } from '@hookform/resolvers/zod';
import { useSetAtom } from 'jotai';
import { useForm } from 'react-hook-form';
import { z } from 'zod';

const schema = z.object({
  email: z.string().email({ message: 'Invalid email address' }),
});

const UnsupportedState = () => {
  const setSteps = useSetAtom(onboardingStepsAtom);
  const { preSignupWaitingList } = useOnboardingService();
  const { nextOnboardingStep } = useOnboardingNavigation();

  const form = useForm<z.infer<typeof schema>>({
    resolver: zodResolver(schema),
    defaultValues: {
      email: '',
    },
  });

  useEffect(() => {
    setSteps({
      stepName: 'Account Creation',
      percentage: 0, // Will be updated by API
      back: '/account/state',
    });
  }, [setSteps]);

  const onSubmit = async (values: z.infer<typeof schema>) => {
    try {
      const response = await preSignupWaitingList.mutateAsync(values);

      // Update the onboarding data with the response
      nextOnboardingStep({
        state: response.currentState,
        context: response.context || {},
        events: [],
        stepName: response.stepName || 'Account Creation',
        percentage: response.percentage || 0, // Backend always calculates
      });
    } catch (error) {
      console.error('Failed to submit email:', error);
    }
  };

  return (
    <OnboardingTitle
      title="We're not available in your state yet"
      subtitle="Unfortunately, Willow is not currently available in your state. We're working hard to expand our services to more locations. Sign up to be notified when we launch in your area."
    >
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)}>
          <FormLoader isLoading={form.formState.isSubmitting}>
            <div className="flex w-full flex-col items-start justify-start gap-10">
              <FormField
                control={form.control}
                name="email"
                render={({ field }) => (
                  <FormItem className="w-full">
                    <FormControl>
                      <Input
                        {...field}
                        type="email"
                        placeholder="Email"
                        className="bg-denim-light py-6 text-white placeholder:text-gray-400"
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <Button
                variant="electric"
                className="flex w-full max-w-none justify-between"
                type="submit"
                size="lg"
                disabled={form.formState.isSubmitting}
              >
                JOIN WAITLIST
                <Image
                  alt="arrow"
                  src={arrow}
                  style={{ objectFit: 'contain' }}
                />
              </Button>
            </div>
          </FormLoader>
        </form>
      </Form>
    </OnboardingTitle>
  );
};

export default UnsupportedState;
