import { StatePersistence } from '@/adapters/persistence/database/state.persistence';
import { ForbiddenException, Injectable, Logger } from '@nestjs/common';
import { fromPromise } from 'xstate';

import type {
  CheckStateEnabledInput,
  CheckStateEnabledOutput,
  OnboardingV1Events,
} from '../states/versions/onboarding-v1.types';
import { PreSignupStateCheckDto } from '../dto/pre-signup/state-check.dto';
import { OnboardingCookieService } from '../services/onboarding-cookie.service';
import { OnboardingStateService } from '../services/onboarding-state.service';
import {
  OnboardingPreSignupBaseUseCase,
  PreSignupResponse,
} from './onboarding-pre-signup-base.use-case';

@Injectable()
export class OnboardingPreSignupStateUseCase extends OnboardingPreSignupBaseUseCase {
  protected readonly logger = new Logger(OnboardingPreSignupStateUseCase.name);

  constructor(
    cookieService: OnboardingCookieService,
    onboardingStateService: OnboardingStateService,
    private readonly statePersistence: StatePersistence,
  ) {
    super(cookieService, onboardingStateService);
  }

  async execute(data: PreSignupStateCheckDto): Promise<PreSignupResponse> {
    // Create the next event with state data
    const event: OnboardingV1Events = {
      type: 'next',
      value: data,
    };

    return this.executeBase(event);
  }

  protected async validateStateRequirements(
    currentState: string,
    event: OnboardingV1Events,
  ): Promise<void> {
    // State selection should only happen in the stateSelection state
    if (!currentState.includes('stateSelection')) {
      throw new ForbiddenException(
        `State selection not allowed in state: ${currentState}`,
      );
    }

    // Validate the event has the required state data
    if (
      event.type !== 'next' ||
      !event.value ||
      typeof event.value !== 'object' ||
      !('state' in event.value)
    ) {
      throw new ForbiddenException('State selection requires state data');
    }
  }

  protected createActor(cookie: any) {
    const actors = {
      checkStateEnabled: fromPromise<
        CheckStateEnabledOutput,
        CheckStateEnabledInput
      >(async ({ input }) => {
        const { stateCode } = input;
        if (!stateCode) {
          throw new Error('No state code provided');
        }

        try {
          const state = await this.statePersistence.getByCode(stateCode);
          if (!state.enabled) {
            throw new Error(`State ${stateCode} is not supported`);
          }
          return { stateCode: state.code };
        } catch (error) {
          throw new Error(`State ${stateCode} is not supported`);
        }
      }),
    };

    return super.createActor(cookie, actors);
  }
}
