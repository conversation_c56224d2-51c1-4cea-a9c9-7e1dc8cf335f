import { PrismaService } from '@/modules/prisma/prisma.service';
import { SnsConsume } from '@/modules/shared/aws/sns/sns.decorator';
import { PatientUpdatedQueueEvent } from '@/modules/shared/events/patient-topic.definition';
import {
  LoggerFactory,
  LoggerService,
} from '@/modules/shared/logger/logger.service';
import { Injectable } from '@nestjs/common';
import { ConversationType } from '@prisma/client';

type ensureConversationTypeParams =
  | {
      conversationType: 'patientDoctor';
      doctorUserId: string;
      patientUserId: string;
    }
  | {
      conversationType: 'doctorAdmin';
      doctorUserId: string;
      patientUserId: string;
      adminUserId: string;
    };

@Injectable()
export class CreateConversationUseCase {
  private readonly logger: LoggerService;

  constructor(
    private readonly prismaService: PrismaService,
    private readonly loggerFactory: LoggerFactory,
  ) {
    this.logger = this.loggerFactory.createLogger(
      CreateConversationUseCase.name,
    );
  }

  @SnsConsume({
    topic: 'patient-updated',
    consumerGroup: 'create-chat-service',
    filter: ['doctor_assigned'],
  })
  async handleSnsMessage({ payload }: PatientUpdatedQueueEvent) {
    const { patient } = payload;
    return this.execute({
      doctorUserId: patient.doctor.userId,
      patientUserId: patient.id,
    });
  }

  async execute({
    doctorUserId,
    patientUserId,
  }: {
    doctorUserId: string;
    patientUserId: string;
  }) {
    return this.ensureConversationType({
      conversationType: 'patientDoctor',
      doctorUserId,
      patientUserId,
    }).catch((err) => {
      // Log and return null if one fails, but continue with others
      this.logger.error(
        err,
        {
          doctorUserId,
          patientUserId,
        },
        `Error ensuring conversation of type patientDoctor:`,
      );
      return null;
    });
  }

  async ensureConversationType(params: ensureConversationTypeParams) {
    const { conversationType, doctorUserId, patientUserId } = params;
    const adminUserId =
      conversationType === 'doctorAdmin' ? params.adminUserId : undefined;

    // Find the conversation by the correct unique index
    let conversation = await this.prismaService.conversation.findUnique({
      where: {
        patientId_type: {
          patientId: patientUserId,
          type: conversationType,
        },
      },
    });

    if (!conversation) {
      conversation = await this.prismaService.conversation.create({
        data: {
          userId: patientUserId,
          patientId: patientUserId,
          type: conversationType,
          status: conversationType === 'doctorAdmin' ? 'open' : 'active',
          assignedAdminId:
            conversationType === 'doctorAdmin' ? adminUserId : null, //If the adminDoctor convo is created by and admin, the convo will be auto-assigned to the admin
          updatedAt: null,
        },
      });
    }

    const watchersToCreate = [
      {
        userId: doctorUserId,
        conversationId: conversation.id,
      },
    ];

    if (conversationType === 'patientDoctor') {
      watchersToCreate.push({
        userId: patientUserId,
        conversationId: conversation.id,
      });
    } else if (
      /*NOTE: if the convo is a doctorAdmin and the adminUserId is not provided
      (like when the convo is created by a doctor) no watcher for admin will be created
      the watcher for admins can still be created when the admin sends a message in the convo
      or when the admin is assigned to the convo
      */
      conversationType == 'doctorAdmin' &&
      typeof adminUserId === 'string'
    ) {
      watchersToCreate.push({
        userId: adminUserId,
        conversationId: conversation.id,
      });
    }

    const existingWatchers =
      await this.prismaService.conversationWatcher.findMany({
        where: {
          conversationId: conversation.id,
          userId: { in: watchersToCreate.map((w) => w.userId) },
        },
        select: { userId: true },
      });

    const existingUserIds = new Set(existingWatchers.map((w) => w.userId));
    const newWatchers = watchersToCreate.filter(
      (w) => !existingUserIds.has(w.userId),
    );

    if (newWatchers.length > 0) {
      await this.prismaService.conversationWatcher.createMany({
        data: newWatchers,
        skipDuplicates: true,
      });
    }

    return conversation.id;
  }
}
