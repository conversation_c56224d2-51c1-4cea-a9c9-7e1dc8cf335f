import { Injectable, Logger } from '@nestjs/common';

import type { OnboardingV1Events } from '../states/versions/onboarding-v1.types';
import {
  OnboardingPreSignupBaseUseCase,
  PreSignupResponse,
} from './onboarding-pre-signup-base.use-case';

@Injectable()
export class OnboardingPreSignupBackUseCase extends OnboardingPreSignupBaseUseCase {
  protected readonly logger = new Logger(OnboardingPreSignupBackUseCase.name);

  async execute(): Promise<PreSignupResponse> {
    const event: OnboardingV1Events = {
      type: 'back',
    };

    return this.executeBase(event);
  }
}
