import type { PatientSignInOutput } from '@modules/shared/types/user/user.types';
import type { Logger } from '@nestjs/common';
import { sanitizeMachineStateForDB } from '@/adapters/persistence/database/patient.persistence';
import { PrismaService } from '@/modules/prisma/prisma.service';
import { ForbiddenException, Injectable } from '@nestjs/common';
import { createActor, waitFor } from 'xstate';

import type { OnboardingV1Events } from '../states/versions/onboarding-v1.types';
import { OnboardingCookieService } from '../services/onboarding-cookie.service';
import { OnboardingStateService } from '../services/onboarding-state.service';
import { onboardingV1Machine } from '../states/versions/onboarding-v1.state';

export type PreSignupResponse =
  | PatientSignInOutput
  | {
      currentState: string;
      canTransition: boolean;
      isComplete: boolean;
      context?: any;
      stepName: string;
      percentage: number;
    };

@Injectable()
export abstract class OnboardingPreSignupBaseUseCase {
  protected abstract readonly logger: Logger;

  constructor(
    protected readonly cookieService: OnboardingCookieService,
    protected readonly onboardingStateService: OnboardingStateService,
    protected readonly prisma?: PrismaService,
  ) {}

  protected async executeBase(
    event: OnboardingV1Events,
  ): Promise<PreSignupResponse> {
    // Get cookie from context
    const cookie = this.cookieService.getCookie();

    if (!cookie) {
      throw new Error('No onboarding cookie found');
    }

    if (cookie.version !== 'v1') {
      throw new Error('Pre-signup flow only available for v1 onboarding');
    }

    if (!cookie.stateSnapshot) {
      throw new Error('No state snapshot found in cookie');
    }

    // Create and start actor
    const actor = this.createActor(cookie);
    actor.start();

    // Validate current state
    const currentState = this.getCurrentState(actor);
    if (!this.isInPreSignup(currentState)) {
      throw new Error('Not in pre-signup state');
    }

    // Validate state-specific requirements
    await this.validateStateRequirements(currentState, event);

    // Check if transition is valid
    if (!actor.getSnapshot().can(event)) {
      throw new ForbiddenException(
        `Invalid state transition from '${currentState}' with event '${event.type}'`,
      );
    }

    // Perform transition
    actor.send(event);

    // Wait for async operations to complete
    const newSnapshot = await this.waitForStateResolution(actor);

    // Update cookie
    this.updateCookie(actor, newSnapshot);

    // If we just created a user and transitioned to questionnaire, update the patient record
    if (
      this.prisma &&
      newSnapshot.context.patientId &&
      this.getStateString(newSnapshot.value).startsWith('questionnaire')
    ) {
      try {
        const persistedSnapshot = actor.getPersistedSnapshot();
        await this.prisma.patient.update({
          where: { id: newSnapshot.context.patientId },
          data: {
            onboardingState: sanitizeMachineStateForDB(persistedSnapshot),
          },
        });
        this.logger.log(
          `Updated patient ${newSnapshot.context.patientId} with questionnaire state`,
        );
      } catch (error) {
        this.logger.error(
          `Failed to update patient onboarding state: ${error}`,
        );
      }
    }

    // Build and return response
    return await this.buildResponse(newSnapshot);
  }

  protected createActor(cookie: any, actors?: any) {
    const machine = actors
      ? onboardingV1Machine.provide({ actors })
      : onboardingV1Machine;

    try {
      if (cookie.stateSnapshot && typeof cookie.stateSnapshot === 'object') {
        return createActor(machine, {
          snapshot: cookie.stateSnapshot as any,
        });
      } else {
        this.logger.warn('Invalid snapshot in cookie, creating fresh actor');
        return createActor(machine, {
          input: {
            version: 'v1' as const,
            existingData: {},
          },
        });
      }
    } catch (error) {
      this.logger.error('Error restoring actor from snapshot:', error);
      return createActor(machine, {
        input: {
          version: 'v1' as const,
          existingData: {},
        },
      });
    }
  }

  protected getCurrentState(actor: any): string {
    try {
      const snapshot = actor.getSnapshot();
      if (!snapshot || !snapshot.value) {
        throw new Error('Invalid actor snapshot');
      }
      return this.getStateString(snapshot.value);
    } catch (error) {
      this.logger.error('Error getting current state:', error);
      throw new Error('Failed to get current state from actor');
    }
  }

  protected getStateString(value: any): string {
    if (typeof value === 'string') {
      return value;
    }
    if (typeof value === 'object' && value !== null) {
      const keys = Object.keys(value as Record<string, any>);
      if (keys.length === 1) {
        const key = keys[0];
        const nestedValue = value[key];
        if (typeof nestedValue === 'object') {
          return `${key}.${this.getStateString(nestedValue)}`;
        }
        return `${key}.${nestedValue}`;
      }
    }
    return String(value);
  }

  protected isInPreSignup(stateString: string): boolean {
    return stateString.startsWith('preSignup');
  }

  protected async waitForStateResolution(actor: any) {
    try {
      return await waitFor(
        actor,
        (state) => {
          const currentStateValue = this.getStateString(state.value);
          return (
            !currentStateValue.includes('checkingState') &&
            !currentStateValue.includes('creatingUser') &&
            !currentStateValue.includes('addingToWaitingList') &&
            !currentStateValue.includes('validatingName')
          );
        },
        { timeout: 30000 },
      );
    } catch (error) {
      this.logger.error('Error waiting for state resolution:', error);
      return actor.getSnapshot();
    }
  }

  protected updateCookie(actor: any, snapshot: any): void {
    try {
      const persistedSnapshot = actor.getPersistedSnapshot();
      const currentCookie = this.cookieService.getCookie();

      // Build update object with state information
      const updates: any = {
        currentState: this.getStateString(snapshot.value),
        stateSnapshot: persistedSnapshot as any,
      };

      // Preserve auth tokens if they exist in the current cookie
      if (currentCookie?.authTokens) {
        updates.authTokens = currentCookie.authTokens;
      }

      this.cookieService.updateCookie(updates);
    } catch (error) {
      this.logger.error('Error updating cookie:', error);
    }
  }

  protected async buildResponse(snapshot: any): Promise<PreSignupResponse> {
    const isComplete = !this.isInPreSignup(this.getStateString(snapshot.value));

    // Always get the onboarding state with percentage calculation
    const onboardingState =
      await this.onboardingStateService.getCurrentOnboardingState(
        'v1',
        snapshot,
      );

    // Check if we have auth data in context - this means account creation was successful
    // The state machine stores the auth data directly in context.authData
    if (snapshot.context.authData && snapshot.context.authData.accessToken) {
      // Merge the auth data with the onboarding profile to create a complete PatientSignInOutput
      const response: PatientSignInOutput = {
        ...snapshot.context.authData,
        onboarding: onboardingState,
      };

      return response;
    }

    // If we're still in pre-signup, return the response with percentage
    const response: any = {
      currentState: this.getStateString(snapshot.value),
      canTransition: true,
      isComplete,
      context: snapshot.context,
      stepName: onboardingState.stepName,
      percentage: onboardingState.percentage,
    };

    return response;
  }

  protected async validateStateRequirements(
    _currentState: string,
    _event: OnboardingV1Events,
  ): Promise<void> {
    // Default implementation: no validation required
    // Subclasses can override this method to add specific validations
  }
}
