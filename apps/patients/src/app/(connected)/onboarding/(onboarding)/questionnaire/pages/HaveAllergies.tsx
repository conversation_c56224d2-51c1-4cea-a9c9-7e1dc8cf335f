import type { OnboardingProps } from '@/app/(connected)/onboarding/(onboarding)/questionnaire/[slug]/page';
import RadioGroupQuestion from '@/components/onboarding/RadioGroupQuestion';

const HaveAllergies = (props: OnboardingProps) => {
  const config = {
    fieldName: 'hasAllergies',
    title: 'Do you have any allergies?',
    subtitle: `Your medical history helps us determine if you're eligible for <PERSON>'s program.`,
    options: [
      { value: 'yes', label: 'Yes' },
      { value: 'no', label: 'No' },
    ],
    schemaValues: ['yes', 'no'] as [string, string],
    errorMessage: 'Please select an option',
    getContextValue: (context: any) => context.questionnaire.hasAllergies,
  };

  return <RadioGroupQuestion {...props} config={config} />;
};

export default HaveAllergies;
