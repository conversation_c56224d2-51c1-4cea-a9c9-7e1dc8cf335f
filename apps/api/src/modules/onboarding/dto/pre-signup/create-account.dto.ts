import { PhoneNumber } from '@modules/shared/validators/phone-number';
import { EmailTopLvlDomain } from '@modules/shared/validators/top-level-domain';
import { Transform } from 'class-transformer';
import {
  IsBoolean,
  IsEmail,
  IsNotEmpty,
  IsOptional,
  Is<PERSON>trong<PERSON>assword,
  <PERSON><PERSON>ength,
  Valida<PERSON>,
} from 'class-validator';

export class PreSignupCreateAccountDto {
  @IsNotEmpty()
  @IsEmail()
  @Transform(({ value }) => value.toLowerCase())
  @Validate(EmailTopLvlDomain)
  email: string;

  @IsStrongPassword({
    minLength: 8,
    minLowercase: 0,
    minNumbers: 0,
    minSymbols: 0,
    minUppercase: 0,
  })
  @IsNotEmpty()
  password: string;

  @IsNotEmpty()
  @Validate(PhoneNumber)
  @MaxLength(25)
  phone: string;

  @IsOptional()
  @IsBoolean()
  getPromotionsSMS?: boolean;
}
