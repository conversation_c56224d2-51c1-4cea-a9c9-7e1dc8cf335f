import { ForbiddenException, Injectable, Logger } from '@nestjs/common';
import { fromPromise } from 'xstate';

import type { OnboardingV1Events } from '../states/versions/onboarding-v1.types';
import { PreSignupNameDto } from '../dto/pre-signup/name.dto';
import { OnboardingCookieService } from '../services/onboarding-cookie.service';
import { OnboardingStateService } from '../services/onboarding-state.service';
import {
  OnboardingPreSignupBaseUseCase,
  PreSignupResponse,
} from './onboarding-pre-signup-base.use-case';

@Injectable()
export class OnboardingPreSignupNameUseCase extends OnboardingPreSignupBaseUseCase {
  protected readonly logger = new Logger(OnboardingPreSignupNameUseCase.name);

  constructor(
    cookieService: OnboardingCookieService,
    onboardingStateService: OnboardingStateService,
  ) {
    super(cookieService, onboardingStateService);
  }

  async execute(data: PreSignupNameDto): Promise<PreSignupResponse> {
    // Create the submit event for name validation
    const event: OnboardingV1Events = {
      type: 'submit',
      subtype: 'validateName',
      value: {
        firstName: data.firstName || '',
        lastName: data.lastName || '',
      },
    };

    return this.executeBase(event);
  }

  protected async validateStateRequirements(
    currentState: string,
    event: OnboardingV1Events,
  ): Promise<void> {
    // Name validation should only happen in the firstAndLastName state
    if (!currentState.includes('firstAndLastName')) {
      throw new ForbiddenException(
        `Name submission not allowed in state: ${currentState}`,
      );
    }

    // Validate the event has the required data
    if (
      event.type !== 'submit' ||
      event.subtype !== 'validateName' ||
      !event.value?.firstName ||
      !event.value?.lastName
    ) {
      throw new ForbiddenException('Invalid name submission data');
    }
  }

  protected createActor(cookie: any) {
    const actors = {
      validateName: fromPromise<
        { valid: boolean; errors?: string[] },
        { firstName: string; lastName: string }
      >(async ({ input }) => {
        const { firstName, lastName } = input;
        const errors: string[] = [];

        // Validate first name
        if (!firstName || firstName.trim().length === 0) {
          errors.push('First name is required');
        } else if (firstName.trim().length < 2) {
          errors.push('First name must be at least 2 characters');
        } else if (!/^[a-zA-Z\s'-]+$/.test(firstName)) {
          errors.push(
            'First name can only contain letters, spaces, hyphens, and apostrophes',
          );
        }

        // Validate last name
        if (!lastName || lastName.trim().length === 0) {
          errors.push('Last name is required');
        } else if (lastName.trim().length < 2) {
          errors.push('Last name must be at least 2 characters');
        } else if (!/^[a-zA-Z\s'-]+$/.test(lastName)) {
          errors.push(
            'Last name can only contain letters, spaces, hyphens, and apostrophes',
          );
        }

        if (errors.length > 0) {
          this.logger.debug('Name validation failed', { errors });
          return { valid: false, errors };
        }

        return { valid: true };
      }),
    };

    return super.createActor(cookie, actors);
  }
}
