import type { OnboardingProps } from '@/app/(connected)/onboarding/(onboarding)/questionnaire/[slug]/page';
import RadioGroupQuestion from '@/components/onboarding/RadioGroupQuestion';

const UsingGLP1 = (props: OnboardingProps) => {
  const config = {
    fieldName: 'usingGLP1',
    title: 'Are you currently using Semaglutide or other GLP-1 medication?',
    subtitle: `For example Dulaglutide (Trulicity), Exanatide (Bydureoan bcise,
        Byetta), Semaglutide (Ozempic, Wegovy, Rybelsus) Liraglutide (Victoza,
        Saxenda), Lixisanatide (Adlyxin) or Tirzepatide (Mounjaro)`,
    options: [
      { value: 'yes', label: 'Yes' },
      { value: 'no', label: 'No' },
    ],
    schemaValues: ['yes', 'no'] as [string, string],
    errorMessage: 'Please select an option',
    getContextValue: (context: any) => context.questionnaire.usingGLP1,
  };

  return <RadioGroupQuestion {...props} config={config} />;
};

export default UsingGLP1;
