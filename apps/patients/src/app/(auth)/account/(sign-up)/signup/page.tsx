'use client';

import React, { useEffect } from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import arrow from '@/assets/svg/arrow-circle.svg';
import OnboardingTitle from '@/components/onboarding/OnboardingTitle';
import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import { Dialog } from '@/components/ui/dialog';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormLoader,
  FormMessage,
} from '@/components/ui/form';
import InputField from '@/components/ui/InputField';
import { PhoneInput } from '@/components/ui/phone-input';
import { useAnalytics } from '@/context/AnalyticsContext';
import { useAnalyticsData } from '@/hooks/useAnalyticsData';
import { useOnboardingNavigation } from '@/hooks/useOnboardingNavigation';
import { useOnboardingService } from '@/hooks/useOnboardingService';
import {
  getStarted<PERSON>tom,
  onboardingDataAtom,
  onboardingDiscountAtom,
  onboardingStepsAtom,
} from '@/store/store';
import { zodResolver } from '@hookform/resolvers/zod';
import { useAtom, useAtomValue, useSetAtom } from 'jotai';
import { useForm } from 'react-hook-form';
import { z } from 'zod';

import { accessTokenAtom } from '@willow/utils/api/auth';

import { getUTMsForBackend } from '~/lib/utm-capture';

const schema = z
  .object({
    email: z
      .string()
      .min(1, { message: 'Email is required' })
      .email('Invalid email address'),
    phone: z.string().min(1, { message: 'Phone is required' }),
    password: z.string().min(8, 'Password must be at least 8 characters.'),
    confirmPassword: z.string(),
    getPromotionsSMS: z.boolean().default(false).optional(),
  })
  .refine((data) => data.password === data.confirmPassword, {
    path: ['confirmPassword'],
    message: 'Passwords does not match',
  });

type SignUpForm = z.infer<typeof schema>;

const SignUp = () => {
  const [getStarted, setGetStarted] = useAtom(getStartedAtom);
  const analyticsData = useAnalyticsData();
  const [isLoading, setIsLoading] = React.useState(false);
  const [showEmailExistsDialog, setShowEmailExistsDialog] =
    React.useState(false);
  const [existingEmail, setExistingEmail] = React.useState('');
  const router = useRouter();
  const setSteps = useSetAtom(onboardingStepsAtom);
  const onboardingData = useAtomValue(onboardingDataAtom);
  const setOnboarding = useSetAtom(onboardingDataAtom);
  const setSignIn = useSetAtom(accessTokenAtom);
  const discount = useAtomValue(onboardingDiscountAtom);
  const analytics = useAnalytics();
  const { preSignupCreateAccount } = useOnboardingService();
  const { nextOnboardingStep } = useOnboardingNavigation();

  const form = useForm<SignUpForm>({
    resolver: zodResolver(schema),
    defaultValues: {
      email: onboardingData?.context?.email || '',
      phone: onboardingData?.context?.phone || '',
      password: '',
      confirmPassword: '',
      getPromotionsSMS: onboardingData?.context?.getPromotionsSMS ?? true,
    },
    shouldFocusError: true,
  });

  useEffect(() => {
    setSteps({
      stepName: 'Account Creation',
      percentage: 0, // Will be updated by API
      back: 'name',
    });
  }, [setSteps]);

  const { firstName } = getStarted;

  const setErrorMessage = (message: string) => {
    const phoneRegex = /phone/i;
    const emailRegex = /email/i;
    const emailExistsRegex = /email.*already.*exists/i;

    if (emailExistsRegex.test(message)) {
      setExistingEmail(form.getValues('email'));
      setShowEmailExistsDialog(true);
    } else if (phoneRegex.test(message)) {
      form.setError('phone', {
        type: 'manual',
        message,
      });
    } else if (emailRegex.test(message)) {
      form.setError('email', {
        type: 'manual',
        message,
      });
    } else {
      form.setError('password', {
        type: 'manual',
        message,
      });
    }
  };

  const onSubmit = async (data: SignUpForm) => {
    try {
      setIsLoading(true);

      // Create the account using the pre-signup flow
      const response = await preSignupCreateAccount.mutateAsync({
        email: data.email,
        phone: data.phone,
        password: data.password,
        getPromotionsSMS: data.getPromotionsSMS,
      });

      const { accessToken, refreshToken, status, patientId } = response;

      // Store authentication tokens
      setSignIn({ accessToken, refreshToken, status } as any);

      // Update onboarding data
      if (response.onboarding) {
        setOnboarding(response.onboarding);
      }

      // Update getStarted atom with email and phone
      setGetStarted((prev) => ({
        ...prev,
        email: data.email,
        phone: data.phone,
      }));

      // Track analytics
      if (analytics) {
        void analytics.identify(patientId, { email: data.email });
        void analytics.track('Account Created', {
          ...analyticsData,
          email: data.email,
          phone: data.phone,
        });
      }

      // Navigate to the questionnaire after successful account creation
      if (response.onboarding) {
        nextOnboardingStep(response.onboarding);
      } else {
        // Fallback to questionnaire if no state provided
        router.push('/onboarding/questionnaire/age');
      }
    } catch (e: any) {
      setIsLoading(false);
      if (Array.isArray(e.response?.data?.message)) {
        e.response.data.message.forEach((message: any) => {
          setErrorMessage(message);
        });
      } else {
        setErrorMessage(e.response?.data?.message || 'An error occurred');
      }
    }
  };

  const handleForgotPassword = () => {
    setGetStarted((prev) => ({
      ...prev,
      email: existingEmail,
    }));
    router.push('/account/forgot-password');
  };

  return (
    <OnboardingTitle
      title={`Nice to meet you, ${firstName}! \n Let's keep in touch.`}
      subtitle="Create an account to save your progress, and let us know where we should contact you about your Willow experience."
    >
      <Form {...form}>
        <form
          onSubmit={form.handleSubmit(onSubmit)}
          className="flex w-full flex-col gap-10"
        >
          <FormLoader isLoading={preSignupCreateAccount.isPending || isLoading}>
            <div className="flex flex-col gap-5">
              <InputField
                control={form.control}
                name="email"
                type="email"
                placeholder="Email"
                variant="dark"
              />

              <PhoneInput
                control={form.control}
                name="phone"
                type="text"
                placeholder="Phone"
                variant="dark"
              />

              <InputField
                control={form.control}
                name="password"
                type="password"
                placeholder="Password"
                variant="dark"
              />
              <InputField
                control={form.control}
                name="confirmPassword"
                type="password"
                placeholder="Confirm Password"
                variant="dark"
              />

              <div className="font-normal text-slate-500">
                Password must be at least 8 characters.
              </div>
            </div>
            <FormField
              control={form.control}
              name="getPromotionsSMS"
              render={({ field }) => (
                <FormItem>
                  <FormControl>
                    <Checkbox
                      id="getPromotionsSMS"
                      checked={field.value}
                      onCheckedChange={field.onChange}
                      className="mr-4 data-[state=checked]:bg-white"
                    />
                  </FormControl>
                  <FormLabel
                    htmlFor="getPromotionsSMS"
                    className="text text-white"
                  >
                    Yes, I'd like to occasionally receive Willow promotions via
                    text.
                  </FormLabel>
                  <FormMessage />
                </FormItem>
              )}
            />
            <Button
              type={'submit'}
              size="lg"
              variant="electric"
              className="flex w-full max-w-none justify-between"
              disabled={preSignupCreateAccount.isPending}
            >
              <span>CONTINUE</span>
              <Image alt="arrow" src={arrow} style={{ objectFit: 'contain' }} />
            </Button>
          </FormLoader>
        </form>
      </Form>

      <div className="font-normal text-slate-500">
        By checking the above box, you agree to receive automated promotional
        messages. This agreement is not a condition of purchase. Message
        frequency varies. Reply STOP to opt-out or HELP for help. Message & data
        rates apply. For questions, please{' '}
        <Link href="https://www.startwillow.com/contact-us" target="_blank">
          contact us
        </Link>
        .{' '}
        <Link href="https://www.startwillow.com/privacy-policy" target="_blank">
          Privacy Policy
        </Link>
      </div>

      <div className="mt-8 flex items-center justify-center gap-2 text-white">
        <span className="text-xl">Already have an account?</span>
        <Link
          href="/"
          className="flex items-center gap-1 text-xl font-medium text-electric"
        >
          Sign in
          <Image
            alt="arrow"
            src={arrow}
            width={20}
            height={20}
            style={{ objectFit: 'contain' }}
          />
        </Link>
      </div>

      {showEmailExistsDialog && (
        <Dialog variant="dark">
          <div className="flex max-w-[400px] flex-col text-white">
            <div className="text-4xl">Account Already Exists</div>
            <div className="mt-4 text-lg">
              An account with this email already exists.
            </div>
            <div className="mt-4 text-lg">Forgot your password?</div>
            <div className="mt-10 w-full">
              <Button
                size="lg"
                variant="electric"
                className="w-full uppercase"
                onClick={handleForgotPassword}
              >
                Click here to reset it
              </Button>
              <Button
                size="lg"
                variant="outlineWhite"
                className="mt-5 w-full uppercase"
                onClick={() => setShowEmailExistsDialog(false)}
              >
                Back
              </Button>
            </div>
          </div>
        </Dialog>
      )}
    </OnboardingTitle>
  );
};

export default SignUp;
