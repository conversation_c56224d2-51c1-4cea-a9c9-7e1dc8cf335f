import { createWriteStream } from 'fs';
import { NestFactory } from '@nestjs/core';

import { AppModule } from './app.module';
import { GetConversationMessagesUseCase } from './modules/chat/use-cases/get-conversation-messages.use-case';
import { WebhooksController } from './modules/intercom/webhooks.controller';
import { PatientGetLetterOfMedicalNecessityUseCase } from './modules/patient/use-cases/patient-get-letter-of-medical-necesity.use-case';
import { PrismaService } from './modules/prisma/prisma.service';

process.env.DATABASE_URL =
  '*****************************************************************************************************************************************************/willow';

process.env.IS_CLI = 'true';
process.env.ENABLE_SUBSCRIPTION_WORKERS = 'false';
process.env.ENABLE_SNS_CONSUMER = 'false';
process.env.ENABLE_SQS_CONSUMER = 'false';

async function testoMedicalNecesityLetter() {
  process.env.POSTGRES_USER = 'outliantwillow';
  process.env.POSTGRES_PASSWORD = 'HealthWwPgwW615!';
  process.env.POSTGRES_PORT = '5432';
  process.env.POSTGRES_HOST =
    'willow-api-staging-rdsinstance9f6b765a-cqbhvi66inwm.citvpurnsqdf.us-west-2.rds.amazonaws.com';
  process.env.POSTGRES_DB = 'willow';
  process.env.DATABASE_URL =
    '********************************************************************************************************************************************/willow';

  process.env.STRIPE_SECRET_KEY =
    'sk_test_51OMDIzK1cV2ssKTgK5aFahjSfLLNCLt4mCNQf7apEIcs3Le7ycD8VQdGGej6IUwJW4cwEXt8PJXeW9N6ztU0lv6o00ZNXamjAI';

  const app = await NestFactory.createApplicationContext(AppModule);
  const patientGetLetterOfMedicalNecessityUseCase = app.get(
    PatientGetLetterOfMedicalNecessityUseCase,
  );

  const prisma = app.get(PrismaService);
  const patient = await prisma.patient.findFirst({
    where: {
      user: {
        email: '<EMAIL>',
      },
    },
  });
  const document = await patientGetLetterOfMedicalNecessityUseCase.execute(
    patient.userId,
  );
  const writeStream = createWriteStream('hsa-receipt.pdf');
  document.getStream().pipe(writeStream);
  writeStream.on('finish', () => {
    process.exit(0);
  });
}

async function patientChat() {
  const app = await NestFactory.createApplicationContext(AppModule);
  const getConversationMessagesUseCase = app.get(
    GetConversationMessagesUseCase,
  );

  const m = await getConversationMessagesUseCase.execute(
    'cbf99d65-8f41-437c-9185-b0dba3521dbe',
    '68c11370-4031-70ae-aa19-60313387009f',
  );
  console.log(m);
}

patientChat();
/*
async function testoWebhookSignature() {
  const app = await NestFactory.createApplicationContext(AppModule);
  const webhooksController = app.get(WebhooksController);
  const result = webhooksController.validateWebhookSignature(
    'sha1=e8ef047f801bd7fd71270e219834bda559ca1ef5',
    JSON.stringify({
      type: 'notification_event',
      app_id: 'ivt1ozil',
      data: {
        type: 'notification_event_data',
        item: {
          type: 'ping',
          message: 'This is a ping notification test message.',
        },
      },
      links: {},
      id: null,
      topic: 'ping',
      delivery_status: null,
      delivery_attempts: 1,
      delivered_at: 0,
      first_sent_at: **********,
      created_at: **********,
      self: null,
    }),
  );
  console.log(result);
  process.exit(0);
}
void testoWebhookSignature();*/
