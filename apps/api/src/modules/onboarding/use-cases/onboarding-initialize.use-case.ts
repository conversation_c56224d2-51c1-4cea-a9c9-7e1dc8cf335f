import { Injectable } from '@nestjs/common';
import { createActor } from 'xstate';

import type { OnboardingInitializeResponse } from '../types/initialize-response.type';
import { OnboardingCookieService } from '../services/onboarding-cookie.service';
import { OnboardingStateService } from '../services/onboarding-state.service';
import { onboardingV1Machine } from '../states/versions/onboarding-v1.state';

@Injectable()
export class OnboardingInitializeUseCase {
  constructor(
    private readonly cookieService: OnboardingCookieService,
    private readonly onboardingStateService: OnboardingStateService,
  ) {}

  async execute(): Promise<OnboardingInitializeResponse> {
    // Initialize endpoint is only used for new patient pre-signup
    // Always use v1 for new patients. Legacy is only for existing patients.
    const version = 'v1' as const;

    // Check for existing cookie first
    const existingCookie = this.cookieService.getCookie();

    if (
      existingCookie &&
      existingCookie.version === 'v1' &&
      existingCookie.stateSnapshot
    ) {
      // Cookie exists with state machine data - restore the state
      // Get the full onboarding state using the state service
      const onboardingState =
        await this.onboardingStateService.getCurrentOnboardingState(
          'v1',
          existingCookie.stateSnapshot,
        );

      const currentState = this.getStateString(onboardingState.state);
      const isInPreSignup = currentState.startsWith('preSignup');

      return {
        version,
        initialized: true,
        currentState,
        canTransition: true,
        isComplete: !isInPreSignup,
        context: onboardingState.context,
        events: onboardingState.events,
        stepName: onboardingState.stepName,
        percentage: onboardingState.percentage,
      };
    }

    // No existing cookie or invalid state - create new state machine
    const actor = createActor(onboardingV1Machine, {
      input: {
        version: 'v1' as const,
        existingData: {},
      },
    });
    actor.start();
    const snapshot = actor.getSnapshot();

    // Get the full onboarding state using the state service
    const onboardingState =
      await this.onboardingStateService.getCurrentOnboardingState(
        'v1',
        snapshot,
      );

    const currentState = this.getStateString(onboardingState.state);

    // Create onboarding cookie with state snapshot
    const cookieData = {
      version: 'v1' as const,
      startedAt: new Date().toISOString(),
      currentState,
      stateSnapshot: actor.getPersistedSnapshot() as any,
    };

    // Set the cookie
    this.cookieService.setCookie(cookieData);

    return {
      version,
      initialized: true,
      currentState,
      canTransition: true,
      isComplete: false,
      context: onboardingState.context,
      events: onboardingState.events,
      stepName: onboardingState.stepName,
      percentage: onboardingState.percentage,
    };
  }

  private getStateString(value: any): string {
    if (typeof value === 'string') {
      return value;
    }
    if (typeof value === 'object' && value !== null) {
      const keys = Object.keys(value as Record<string, any>);
      if (keys.length === 1) {
        const key = keys[0];
        const nestedValue = value[key];
        if (typeof nestedValue === 'object') {
          return `${key}.${this.getStateString(nestedValue)}`;
        }
        return `${key}.${nestedValue}`;
      }
    }
    return String(value);
  }
}
